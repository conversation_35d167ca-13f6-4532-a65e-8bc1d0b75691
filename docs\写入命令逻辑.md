写入命令逻辑介绍：
写入命令有且只有以下几种格式：

# 1.A1 启动指令
发送 固定报文：EB 90 01 01 A1 00 00 00 00 00 00 00 00 1E AA AB
接收 固定报文：EB 90 01 01 A1 00 00 00 00 00 00 00 00 1E AA AB
接收到固定报文后确认A1指令成功
# 2.A2 停止指令
发送 固定报文：EB 90 01 01 A2 00 00 00 00 00 00 00 00 1F AA AB
接收 固定报文：EB 90 01 01 A2 00 00 00 00 00 00 00 00 1F AA AB
接收到固定报文后确认A2指令成功
# 3.A3 暂停指令
发送 固定报文：EB 90 01 01 A3 00 00 00 00 00 00 00 00 20 AA AB
接收 固定报文：EB 90 01 01 A3 00 00 00 00 00 00 00 00 20 AA AB
接收到固定报文后确认A3指令成功
# 4.A4 无功启动指令
发送 固定报文：EB 90 01 01 A4 00 00 00 00 00 00 00 00 21 AA AB
接收 固定报文：EB 90 01 01 A4 00 00 00 00 00 00 00 00 21 AA AB
接收到固定报文后确认A4指令成功
# 5.A5 谐波消除指令
发送 固定报文：EB 90 01 01 A5 00 00 00 00 00 00 00 00 22 AA AB
接收 固定报文：EB 90 01 01 A5 00 00 00 00 00 00 00 00 22 AA AB
接收到固定报文后确认A5指令成功
# 6.A6 综合控制指令
发送 固定报文：EB 90 01 01 A6 00 00 00 00 00 00 00 00 23 AA AB
接收 固定报文：EB 90 01 01 A6 00 00 00 00 00 00 00 00 23 AA AB
接收到固定报文后确认A6指令成功
# 7.AA 水冷启动指令
发送 固定报文：EB 90 01 01 AA 00 00 00 00 00 00 00 00 27 AA AB
接收 固定报文：EB 90 01 01 AA 00 00 00 00 00 00 00 00 27 AA AB
接收到固定报文后确认AA指令成功
# 8.AB 水冷停止指令
发送 固定报文：EB 90 01 01 AB 00 00 00 00 00 00 00 00 28 AA AB
接收 固定报文：EB 90 01 01 AB 00 00 00 00 00 00 00 00 28 AA AB
接收到固定报文后确认AB指令成功

# 59：调试参数 查询帧，修改帧，修改返回帧，返回帧，修改完成确认帧，修改完成确认返回帧等规则

## 查询帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：59（查询所有调试参数）
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：57
* **数据字节数**：0x04（单字节）
* **空字节占位**：00 （单个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **数据帧**：00 00 00 00 (共4️个字节；小端模式DCBA；转换标准为IEEE754)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：55
* **空字节占位**：00 00（两个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **空字节占位**：00 00 00 00 (共4️个空字节；)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 返回帧（每帧返回一个序号的数据，总共返回259帧）
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：59
* **数据字节数**：0x04（单字节）
* **空字节占位**：00 （单个空字节）
* **序号位**：00 00 （两个字节；小端模式BA）
* **数据帧**：00 00 00 00 (共4️个字节；小端模式DCBA；转换标准为IEEE754)
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改完成确认帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：37
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

## 修改完成确认返回帧
* **帧头**：EB 90 01 01 （固定4️字节）
* **命令**：5B
* **空字节占位**：00 00 00 00 00 00 00 00 （8个空字节）
* **校验位**：单字节
* **帧尾**：AA AB （固定双字节）
* **校验规则**：（单字节）和校验（HEX）

# 查询流程
**发送59查询命令**==>**接收259帧返回数据**


# 修改流程
**发送57修改命令**==>**接收55修改放回帧**==>**发送37修改完成确认命令**==>**接收5B修改完成确认帧**==>**发送59查询命令**==>**接收259帧返回数据**




在未触发修改下发指令时，服务一直按照这个顺序循环（C1→C4→D0→D1→D2→C4→D3→D4→D5→C4→C0）下发指令和接收报文。当触发修改下发指令时，在执行完当前循环中正在进行的某一指令后优先执行手动下发的指令，执行完修改下发的指令后接着之前的顺序执行循环中的指令

修改调试参数的逻辑实现在循环命令中等待上一个命令执行完后下发（发送57命令修改==>接收55修改确认==>发送37所有修改完确认==>接收5B所有已修改完确认==>发送59命令查询所有修改后调试参数）等待59查询后所有数据帧接收完后继续执行剩下的循环指令；
其他修改命令（A1-A6；AA；AB）均在循环命令中等待上一个命令执行完后下发，接收到对应返回报文后继续执行剩下循环命令；