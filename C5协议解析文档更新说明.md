# C5版本信息协议解析文档更新说明

## 更新概述

根据提供的样例数据，已完成对 `docs\C5版本信息协议解析文档.md` 的全面更新，主要修正了字节序解析规则和模块映射关系。

## 主要更新内容

### 1. 字节序解析规则修正

**原解析规则**：
- 第4个字节：版本号
- 前3个字节：日期（需要字节序转换）

**新解析规则**：
- 第1个字节：版本号
- 后3个字节：日期（BCD码格式，无需字节序转换）

### 2. 模块映射更新

**更新后的模块映射**：
- 组1-8：标准模块解析
- 组9-11：跳过解析（未定义模块）
- 组12：特殊复合解析（包含两个子模块）

### 3. 样例数据对应关系

| 组号 | 原始字节 | 模块名称 | 版本号 | 修改日期 |
|------|----------|----------|--------|----------|
| 1 | 83 15 09 23 | 主控CPU板DSP版本号 | 8.3 | 15-09-23 |
| 2 | 20 25 03 06 | 主控CPU板CPLD版本号 | 2.0 | 25-03-06 |
| 3 | 88 88 88 88 | 主控A相PWM板DSP版本号 | 8.8 | 88-88-88 |
| 4 | 20 25 01 03 | 主控A相PWM板FPGA版本号 | 2.0 | 25-01-03 |
| 5 | 88 88 88 88 | 主控B相PWM板DSP版本号 | 8.8 | 88-88-88 |
| 6 | 20 25 01 03 | 主控B相PWM板FPGA版本号 | 2.0 | 25-01-03 |
| 7 | 88 88 88 88 | 主控C相PWM板DSP版本号 | 8.8 | 88-88-88 |
| 8 | 20 25 01 03 | 主控C相PWM板FPGA版本号 | 2.0 | 25-01-03 |
| 12 | 01 00 00 01 | 复合版本信息 | 见下表 | 见下表 |

### 4. 第12组复合版本信息解析

| 子模块 | 字节位置 | 模块名称 | 版本号 | 修改日期 |
|--------|----------|----------|--------|----------|
| 12A | 字节1-2 (01 00) | 主控CPU板DSP子版本号 | 00.01 | 00-00-00 |
| 12B | 字节3-4 (00 01) | 主控CPU板IO板版本号 | 0.0 | 00-00-01 |

## 技术实现更新

### 1. 解析函数结构

```python
def parse_version_info(data):
    # 只解析指定的模块（组1-8, 12）
    # 跳过组9-11（未定义模块）
    # 第12组使用特殊复合解析
```

### 2. 标准组解析

```python
def parse_standard_group(group_data):
    version_byte = group_data[0]  # 第1个字节是版本号
    date_bytes = group_data[1:4]  # 后3个字节是日期（BCD码）
```

### 3. 复合组解析

```python
def parse_group_12_composite(group_data):
    # 字节1-2: 主控CPU板DSP子版本号
    # 字节3-4: 主控CPU板IO板版本号
```

## 验证结果

通过测试脚本 `test_updated_c5_parser.py` 验证，所有解析结果与样例数据完全匹配：

```
✓ 主控CPU板DSP版本号: 版本8.3, 日期15-09-23 - 正确
✓ 主控CPU板CPLD版本号: 版本2.0, 日期25-03-06 - 正确
✓ 主控A相PWM板DSP版本号: 版本8.8, 日期88-88-88 - 正确
✓ 主控A相PWM板FPGA版本号: 版本2.0, 日期25-01-03 - 正确
✓ 主控B相PWM板DSP版本号: 版本8.8, 日期88-88-88 - 正确
✓ 主控B相PWM板FPGA版本号: 版本2.0, 日期25-01-03 - 正确
✓ 主控C相PWM板DSP版本号: 版本8.8, 日期88-88-88 - 正确
✓ 主控C相PWM板FPGA版本号: 版本2.0, 日期25-01-03 - 正确
✓ 主控CPU板DSP子版本号: 版本00.01, 日期00-00-00 - 正确
✓ 主控CPU板IO板版本号: 版本0.0, 日期00-00-01 - 正确

✓ 所有模块解析结果正确！
```

## 文档更新清单

### 已更新的章节

1. **第4.3节 逐组解析**
   - 更新解析表格，使用新的样例数据
   - 修正字节序表示方式
   - 新增第12组复合版本信息解析表

2. **第4.4节 解析规则说明**
   - 修正基本解析规则
   - 明确字节序处理方式
   - 新增模块解析策略
   - 详细说明第12组复合解析规则

3. **第5节 Python代码示例**
   - 完全重写解析函数
   - 新增标准组解析函数
   - 新增复合组解析函数
   - 新增BCD码转换函数
   - 更新使用示例和测试数据

4. **第8.2节 版本号格式化**
   - 修正版本号和日期的字节位置说明
   - 新增复合解析说明

5. **JSON存储示例**
   - 更新示例数据格式
   - 新增复合版本信息示例

## 关键技术要点

1. **字节序处理**：按实际接收顺序解析，无需字节序转换
2. **BCD码解析**：日期字段使用BCD码格式
3. **选择性解析**：只解析指定的模块，跳过未定义模块
4. **复合解析**：第12组包含两个不同的版本信息
5. **向后兼容**：保持原有的数据结构和接口

## 测试文件

- `test_updated_c5_parser.py`：验证更新后解析规则的测试脚本
- 测试通过率：100%（10/10个模块解析正确）

## 总结

本次更新完全按照提供的样例数据修正了C5版本信息协议解析文档，确保解析结果与实际数据格式完全匹配。更新后的文档和代码已通过全面测试验证，可以正确解析所有指定的模块版本信息。
