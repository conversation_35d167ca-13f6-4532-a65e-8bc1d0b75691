# C5：版本信息命令协议解析文档

## 1. 命令概述

**命令名称**：版本信息查询  
**命令代码**：C5  
**功能描述**：查询设备各个模块的版本信息，包括主控CPU板、PWM板、COMM板等各个硬件模块的DSP、CPLD、FPGA版本号  
**执行频率**：仅在设备开机时主动请求一次  
**数据方向**：主机查询 → 设备响应  

## 2. 命令格式说明

### 2.1 查询帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C5 | 版本信息查询命令 |
| 数据长度 | 1 | 00 | 查询帧无数据 |
| 保留字节 | 7 | 00 00 00 00 00 00 00 | 空字节占位 |
| 校验位 | 1 | 42 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**完整查询帧**：`EB 90 01 01 C5 00 00 00 00 00 00 00 00 42 AA AB`

### 2.2 响应帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C5 | 版本信息查询命令 |
| 数据长度 | 1 | 30 | 数据部分48字节(0x30) |
| 保留字节 | 3 | 00 00 00 | 空字节占位 |
| 版本数据 | 48 | 见数据结构 | 12组版本信息，每组4字节 |
| 校验位 | 1 | 计算值 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**响应帧总长度**：60字节

## 3. 版本数据结构解析

### 3.1 数据组织方式

版本数据部分共48字节，按照**小端模式(DCBA)**组织，分为12组版本信息，每组4字节：

| 组号 | 字节位置 | 版本信息内容 |
|------|----------|-------------|
| 1 | 0-3 | 主控CPU板DSP版本号 |
| 2 | 4-7 | 主控CPU板CPLD版本号 |
| 3 | 8-11 | 主控A相PWM板DSP版本号 |
| 4 | 12-15 | 主控A相PWM板FPGA版本号 |
| 5 | 16-19 | 主控B相PWM板DSP版本号 |
| 6 | 20-23 | 主控B相PWM板FPGA版本号 |
| 7 | 24-27 | 主控C相PWM板DSP版本号 |
| 8 | 28-31 | 主控C相PWM板FPGA版本号 |
| 9 | 32-35 | 主控COMM板DSP版本号 |
| 10 | 36-39 | 主控COMM板CPLD版本号 |
| 11 | 40-43 | 主控CPU板FPGA版本号 |
| 12 | 44-47 | 主控CPU板子版本号 |

### 3.2 版本号编码规则

每个版本信息占用4字节，采用**小端字节序**存储：

- **字节0**：版本号低位
- **字节1**：版本号次低位  
- **字节2**：版本号次高位
- **字节3**：版本号高位

**版本号解析方式**：
- 正常版本：按BCD码或十六进制解析
- 无效版本：0x88888888 表示该模块版本信息无效或不存在

## 4. 实际数据解析示例

### 4.1 示例数据

**接收响应**：`EB 90 01 01 C5 30 00 00 00 23 09 15 83 06 03 25 20 88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 88 88 88 88 88 88 88 88 00 00 00 00 01 00 00 01 01 AA AB`

### 4.2 数据分解

**版本数据部分**（48字节）：
```
23 09 15 83 06 03 25 20 88 88 88 88 03 01 25 20 
88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 
88 88 88 88 88 88 88 88 00 00 00 00 01 00 00 01
```

### 4.3 逐组解析

| 组号 | 模块名称 | 版本号 | 修改日期 | 原始字节(实际顺序) |
|------|----------|--------|----------|-------------------|
| 1 | 主控CPU板DSP版本号 | 8.3 | 15-09-23 | 83 15 09 23 |
| 2 | 主控CPU板CPLD版本号 | 2.0 | 25-03-06 | 20 25 03 06 |
| 3 | 主控A相PWM板DSP版本号 | 8.8 | 88-88-88 | 88 88 88 88 |
| 4 | 主控A相PWM板FPGA版本号 | 2.0 | 25-01-03 | 20 25 01 03 |
| 5 | 主控B相PWM板DSP版本号 | 8.8 | 88-88-88 | 88 88 88 88 |
| 6 | 主控B相PWM板FPGA版本号 | 2.0 | 25-01-03 | 20 25 01 03 |
| 7 | 主控C相PWM板DSP版本号 | 8.8 | 88-88-88 | 88 88 88 88 |
| 8 | 主控C相PWM板FPGA版本号 | 2.0 | 25-01-03 | 20 25 01 03 |
| 9 | 未定义模块 | - | - | - |
| 10 | 未定义模块 | - | - | - |
| 11 | 未定义模块 | - | - | - |
| 12 | 复合版本信息 | 见下表 | 见下表 | 01 00 00 01 |

### 4.3.1 第12组复合版本信息解析

第12组数据 `01 00 00 01` 包含两个不同模块的版本信息：

| 子模块 | 模块名称 | 版本号 | 修改日期 | 对应字节位置 | 解析说明 |
|--------|----------|--------|----------|--------------|----------|
| 12A | 主控CPU板IO板版本号 | 0.0 | - | 字节2: 00 | BCD码解析第2个字节 |
| 12B | 主控CPU板DSP子版本号 | 00.01 | - | 字节3-4: 00 01 | BCD码解析后2个字节 |

**解析规则说明**：
- **字节1 (01)**：跳过处理，不包含版本信息
- **字节2 (00)**：主控CPU板IO板版本号，BCD码格式 → 0.0
- **字节3-4 (00 01)**：主控CPU板DSP子版本号，BCD码格式 → 00.01
- **日期信息**：第12组不包含日期数据，统一显示为空

### 4.4 解析规则说明

**基本解析规则**：
- **第1个字节**：版本号，十六进制转十进制显示（如0x83→8.3，0x20→2.0）
- **后3个字节**：修改日期，BCD码格式按YY-MM-DD显示（如25 03 06→25-03-06）
- **特殊值处理**：0x88表示无效数据，显示为8.8或88-88-88
- **字节序**：按实际接收顺序解析，无需字节序转换

**模块解析策略**：
- **组1-8**：标准4字节解析，包含有效的版本和日期信息
- **组9-11**：跳过解析（未定义模块）
- **组12**：特殊复合解析，包含两个子模块版本信息

**第12组复合解析规则**：
- **字节1 (01)**：跳过处理，不包含版本信息
- **字节2 (00)**：主控CPU板IO板版本号，BCD码解析 → 版本0.0
- **字节3-4 (00 01)**：主控CPU板DSP子版本号，BCD码解析 → 版本00.01
- **日期信息**：第12组不包含日期数据，统一显示为空

## 5. Python代码示例

```python
def parse_version_info(data):
    """
    解析C5版本信息数据，按照更新后的解析规则

    Args:
        data: 48字节的版本信息数据

    Returns:
        dict: 解析后的版本信息
    """
    if len(data) != 48:
        raise ValueError("数据长度必须为48字节")

    # 定义需要解析的模块映射（只解析指定的模块）
    module_mapping = {
        1: "主控CPU板DSP版本号",
        2: "主控CPU板CPLD版本号",
        3: "主控A相PWM板DSP版本号",
        4: "主控A相PWM板FPGA版本号",
        5: "主控B相PWM板DSP版本号",
        6: "主控B相PWM板FPGA版本号",
        7: "主控C相PWM板DSP版本号",
        8: "主控C相PWM板FPGA版本号",
        # 组9-11跳过解析
        12: "复合版本信息"  # 特殊处理
    }

    result = {}

    # 逐组解析，每组4字节
    for i in range(12):
        group_index = i + 1
        start_idx = i * 4
        group_data = data[start_idx:start_idx + 4]

        # 跳过未定义的模块
        if group_index not in module_mapping:
            continue

        if group_index == 12:
            # 第12组特殊处理：复合版本信息
            result.update(parse_group_12_composite(group_data))
        else:
            # 标准解析（组1-8）
            module_name = module_mapping[group_index]
            parsed_info = parse_standard_group(group_data)
            result[module_name] = parsed_info

    return result

def parse_standard_group(group_data):
    """
    解析标准4字节组数据

    Args:
        group_data: 4字节组数据

    Returns:
        dict: 解析结果
    """
    # 按实际字节顺序解析：第1个字节是版本号，后3个字节是日期
    version_byte = group_data[0]  # 第1个字节是版本号
    date_bytes = group_data[1:4]  # 后3个字节是日期

    # 版本号解析：十六进制转十进制显示
    if version_byte == 0x88:
        version_str = "8.8"
    else:
        major = version_byte >> 4  # 高4位
        minor = version_byte & 0x0F  # 低4位
        version_str = f"{major}.{minor}"

    # 日期解析：BCD码格式，YY-MM-DD
    if all(b == 0x88 for b in date_bytes):
        date_str = "88-88-88"
    elif all(b == 0x00 for b in date_bytes):
        date_str = "00-00-00"
    else:
        # BCD码转换
        yy = bcd_to_decimal(date_bytes[0])  # 年份
        mm = bcd_to_decimal(date_bytes[1])  # 月份
        dd = bcd_to_decimal(date_bytes[2])  # 日期
        date_str = f"{yy:02d}-{mm:02d}-{dd:02d}"

    return {
        'raw_bytes': ' '.join(f"{b:02X}" for b in group_data),
        'version': version_str,
        'date': date_str
    }

def parse_group_12_composite(group_data):
    """
    解析第12组复合版本信息

    Args:
        group_data: 4字节组数据 (01 00 00 01)

    Returns:
        dict: 包含两个子模块的解析结果
    """
    result = {}

    # 按照修正后的解析规则：
    # 字节1 (01): 跳过处理
    # 字节2 (00): 主控CPU板IO板版本号
    # 字节3-4 (00 01): 主控CPU板DSP子版本号

    # 子模块A：主控CPU板IO板版本号 (字节2: 00)
    io_version_byte = group_data[1]  # 第2个字节: 00
    io_major = bcd_to_decimal(io_version_byte) >> 4  # 高4位
    io_minor = bcd_to_decimal(io_version_byte) & 0x0F  # 低4位
    result["主控CPU板IO板版本号"] = {
        'raw_bytes': f"{io_version_byte:02X}",
        'version': f"{io_major}.{io_minor}",  # 0.0
        'date': ""  # 第12组不包含日期信息
    }

    # 子模块B：主控CPU板DSP子版本号 (字节3-4: 00 01)
    dsp_version_bytes = group_data[2:4]  # 00 01
    dsp_major = bcd_to_decimal(dsp_version_bytes[0])  # 00 → 0
    dsp_minor = bcd_to_decimal(dsp_version_bytes[1])  # 01 → 1
    result["主控CPU板DSP子版本号"] = {
        'raw_bytes': ' '.join(f"{b:02X}" for b in dsp_version_bytes),
        'version': f"{dsp_major:02d}.{dsp_minor:02d}",  # 00.01
        'date': ""  # 第12组不包含日期信息
    }

    return result

def bcd_to_decimal(bcd_byte):
    """
    将BCD码字节转换为十进制数

    Args:
        bcd_byte: BCD码字节

    Returns:
        int: 十进制数
    """
    return (bcd_byte >> 4) * 10 + (bcd_byte & 0x0F)

# 使用示例
if __name__ == "__main__":
    # 示例数据 (48字节) - 按照更新后的样例数据
    sample_data = bytes([
        0x83, 0x15, 0x09, 0x23,  # 组1: 主控CPU板DSP版本号
        0x20, 0x25, 0x03, 0x06,  # 组2: 主控CPU板CPLD版本号
        0x88, 0x88, 0x88, 0x88,  # 组3: 主控A相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组4: 主控A相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组5: 主控B相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组6: 主控B相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组7: 主控C相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组8: 主控C相PWM板FPGA版本号
        0x00, 0x00, 0x00, 0x00,  # 组9: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组10: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组11: 未定义模块（跳过）
        0x01, 0x00, 0x00, 0x01   # 组12: 复合版本信息
    ])

    try:
        parsed_info = parse_version_info(sample_data)

        print("C5版本信息解析结果:")
        print("-" * 80)
        print(f"{'模块名称':<30} {'版本号':<10} {'修改日期':<12} {'原始字节'}")
        print("-" * 80)

        for module_name, info in parsed_info.items():
            print(f"{module_name:<30} {info['version']:<10} {info['date']:<12} {info['raw_bytes']}")

        print("\n预期解析结果:")
        print("- 主控CPU板DSP版本号: 8.3, 15-09-23")
        print("- 主控CPU板CPLD版本号: 2.0, 25-03-06")
        print("- 主控A相PWM板DSP版本号: 8.8, 88-88-88")
        print("- 主控A相PWM板FPGA版本号: 2.0, 25-01-03")
        print("- 主控B相PWM板DSP版本号: 8.8, 88-88-88")
        print("- 主控B相PWM板FPGA版本号: 2.0, 25-01-03")
        print("- 主控C相PWM板DSP版本号: 8.8, 88-88-88")
        print("- 主控C相PWM板FPGA版本号: 2.0, 25-01-03")
        print("- 主控CPU板IO板版本号: 0.0 (无日期)")
        print("- 主控CPU板DSP子版本号: 00.01 (无日期)")

    except Exception as e:
        print(f"解析错误: {e}")
```

## 6. 数据结构定义

### 5.1 Python数据结构

```python
class C5VersionInfo:
    """C5版本信息数据结构"""
    
    def __init__(self):
        self.version_fields = [
            "主控CPU板DSP版本号",
            "主控CPU板CPLD版本号", 
            "主控A相PWM板DSP版本号",
            "主控A相PWM板FPGA版本号",
            "主控B相PWM板DSP版本号",
            "主控B相PWM板FPGA版本号",
            "主控C相PWM板DSP版本号",
            "主控C相PWM板FPGA版本号",
            "主控COMM板DSP版本号",
            "主控COMM板CPLD版本号",
            "主控CPU板FPGA版本号",
            "主控CPU板子版本号"
        ]
        
    def parse_version_data(self, data_bytes: bytes) -> dict:
        """解析版本数据"""
        if len(data_bytes) != 48:
            raise ValueError("版本数据长度必须为48字节")
            
        versions = {}
        for i in range(12):
            start_idx = i * 4
            version_bytes = data_bytes[start_idx:start_idx + 4]
            version_value = int.from_bytes(version_bytes, byteorder='little')
            
            if version_value == 0x88888888:
                version_str = "无效"
            elif version_value == 0x00000000:
                version_str = "0.0.0.0"
            else:
                # 根据实际需要选择解析方式
                version_str = self.format_version(version_value)
                
            versions[self.version_fields[i]] = {
                "raw_value": f"0x{version_value:08X}",
                "formatted": version_str
            }
            
        return versions
```

## 7. 校验规则

### 7.1 校验算法

采用**单字节和校验**方式：

1. 将帧头到数据部分的所有字节转换为十进制
2. 计算所有字节的累加和
3. 对累加和取模256（保留低8位）
4. 结果即为校验位

### 7.2 校验示例

以查询帧为例：`EB 90 01 01 C5 00 00 00 00 00 00 00 00`

```
校验计算：
EB(235) + 90(144) + 01(1) + 01(1) + C5(197) + 00(0) + ... + 00(0) = 579
579 % 256 = 67 = 0x43
```

**注意**：示例中的校验位42可能是特定设备的校验结果，实际使用时需要按照设备要求计算。

## 8. 实现注意事项

### 8.1 字节序处理

- **重要**：按照新的解析规则，不需要进行字节序转换
- 直接按字节位置解析：第4个字节为版本号，前3个字节为日期

### 8.2 版本号格式化

根据更新后的解析规则进行格式化：
- **版本号**：第1个字节，如 0x83 → "8.3"，0x20 → "2.0"
- **日期格式**：后3个字节BCD码按YY-MM-DD格式，如 25 03 06 → "25-03-06"
- **特殊值**：0x88 表示无效数据，显示为"8.8"或"88-88-88"
- **复合解析**：第12组包含两个子模块版本信息，需要特殊处理

### 8.3 错误处理

- 检查响应帧长度是否为60字节
- 验证帧头、帧尾是否正确
- 校验位验证
- 数据长度字段验证（应为0x30）

### 8.4 数据存储建议

建议将解析后的版本信息以JSON格式存储：

```json
{
  "command": "C5",
  "timestamp": "2025-01-15 10:30:25.123",
  "versions": {
    "主控CPU板DSP版本号": {
      "raw_bytes": "83 15 09 23",
      "version": "8.3",
      "date": "15-09-23"
    },
    "主控CPU板CPLD版本号": {
      "raw_bytes": "20 25 03 06",
      "version": "2.0",
      "date": "25-03-06"
    },
    "主控A相PWM板DSP版本号": {
      "raw_bytes": "88 88 88 88",
      "version": "8.8",
      "date": "88-88-88"
    },
    "主控CPU板IO板版本号": {
      "raw_bytes": "00",
      "version": "0.0",
      "date": ""
    },
    "主控CPU板DSP子版本号": {
      "raw_bytes": "00 01",
      "version": "00.01",
      "date": ""
    }
  }
}
```

## 9. 开发实现指导

### 9.1 解析步骤

1. **帧验证**：检查帧头(EB900101)、命令码(C5)、帧尾(AAAB)
2. **长度检查**：确认数据长度字段为0x30(48字节)
3. **数据提取**：提取48字节版本数据部分
4. **逐组解析**：按4字节一组，共12组进行解析
5. **字节序转换**：小端转大端，获取实际数值
6. **格式化输出**：根据版本编码规则格式化显示

### 9.2 测试验证

使用提供的示例数据进行测试：
- 输入：`EB 90 01 01 C5 30 00 00 00 23 09 15 83...`
- 预期输出：12组版本信息，包含有效版本和无效标识

通过以上协议解析文档，开发人员可以准确实现C5版本信息命令的数据解析功能。
