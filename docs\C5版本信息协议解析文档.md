# C5：版本信息命令协议解析文档

## 1. 命令概述

**命令名称**：版本信息查询  
**命令代码**：C5  
**功能描述**：查询设备各个模块的版本信息，包括主控CPU板、PWM板、COMM板等各个硬件模块的DSP、CPLD、FPGA版本号  
**执行频率**：仅在设备开机时主动请求一次  
**数据方向**：主机查询 → 设备响应  

## 2. 命令格式说明

### 2.1 查询帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C5 | 版本信息查询命令 |
| 数据长度 | 1 | 00 | 查询帧无数据 |
| 保留字节 | 7 | 00 00 00 00 00 00 00 | 空字节占位 |
| 校验位 | 1 | 42 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**完整查询帧**：`EB 90 01 01 C5 00 00 00 00 00 00 00 00 42 AA AB`

### 2.2 响应帧格式

| 字段名称 | 字节数 | 十六进制值 | 说明 |
|---------|--------|-----------|------|
| 帧头 | 4 | EB 90 01 01 | 固定帧头标识 |
| 命令码 | 1 | C5 | 版本信息查询命令 |
| 数据长度 | 1 | 30 | 数据部分48字节(0x30) |
| 保留字节 | 3 | 00 00 00 | 空字节占位 |
| 版本数据 | 48 | 见数据结构 | 12组版本信息，每组4字节 |
| 校验位 | 1 | 计算值 | 单字节和校验 |
| 帧尾 | 2 | AA AB | 固定帧尾标识 |

**响应帧总长度**：60字节

## 3. 版本数据结构解析

### 3.1 数据组织方式

版本数据部分共48字节，按照**小端模式(DCBA)**组织，分为12组版本信息，每组4字节：

| 组号 | 字节位置 | 版本信息内容 |
|------|----------|-------------|
| 1 | 0-3 | 主控CPU板DSP版本号 |
| 2 | 4-7 | 主控CPU板CPLD版本号 |
| 3 | 8-11 | 主控A相PWM板DSP版本号 |
| 4 | 12-15 | 主控A相PWM板FPGA版本号 |
| 5 | 16-19 | 主控B相PWM板DSP版本号 |
| 6 | 20-23 | 主控B相PWM板FPGA版本号 |
| 7 | 24-27 | 主控C相PWM板DSP版本号 |
| 8 | 28-31 | 主控C相PWM板FPGA版本号 |
| 9 | 32-35 | 主控COMM板DSP版本号 |
| 10 | 36-39 | 主控COMM板CPLD版本号 |
| 11 | 40-43 | 主控CPU板FPGA版本号 |
| 12 | 44-47 | 主控CPU板子版本号 |

### 3.2 版本号编码规则

每个版本信息占用4字节，采用**小端字节序**存储：

- **字节0**：版本号低位
- **字节1**：版本号次低位  
- **字节2**：版本号次高位
- **字节3**：版本号高位

**版本号解析方式**：
- 正常版本：按BCD码或十六进制解析
- 无效版本：0x88888888 表示该模块版本信息无效或不存在

## 4. 实际数据解析示例

### 4.1 示例数据

**接收响应**：`EB 90 01 01 C5 30 00 00 00 23 09 15 83 06 03 25 20 88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 88 88 88 88 88 88 88 88 00 00 00 00 01 00 00 01 01 AA AB`

### 4.2 数据分解

**版本数据部分**（48字节）：
```
23 09 15 83 06 03 25 20 88 88 88 88 03 01 25 20 
88 88 88 88 03 01 25 20 88 88 88 88 03 01 25 20 
88 88 88 88 88 88 88 88 00 00 00 00 01 00 00 01
```

### 4.3 逐组解析

| 组号 | 原始字节(小端) | 转换后值 | 版本信息 | 解析结果 |
|------|---------------|----------|----------|----------|
| 1 | 23 09 15 83 | 0x83150923 | 主控CPU板DSP版本号 | 8.3.15.83 |
| 2 | 06 03 25 20 | 0x20250306 | 主控CPU板CPLD版本号 | 25-03-06 |
| 3 | 88 88 88 88 | 0x88888888 | 主控A相PWM板DSP版本号 | 无效 |
| 4 | 03 01 25 20 | 0x20250103 | 主控A相PWM板FPGA版本号 | 25-01-03 |
| 5 | 88 88 88 88 | 0x88888888 | 主控B相PWM板DSP版本号 | 无效 |
| 6 | 03 01 25 20 | 0x20250103 | 主控B相PWM板FPGA版本号 | 25-01-03 |
| 7 | 88 88 88 88 | 0x88888888 | 主控C相PWM板DSP版本号 | 无效 |
| 8 | 03 01 25 20 | 0x20250103 | 主控C相PWM板FPGA版本号 | 25-01-03 |
| 9 | 88 88 88 88 | 0x88888888 | 主控COMM板DSP版本号 | 无效 |
| 10 | 88 88 88 88 | 0x88888888 | 主控COMM板CPLD版本号 | 无效 |
| 11 | 00 00 00 00 | 0x00000000 | 主控CPU板FPGA版本号 | 0.0.0.0 |
| 12 | 01 00 00 01 | 0x01000001 | 主控CPU板子版本号 | 1.0.0.1 |

## 5. 数据结构定义

### 5.1 Python数据结构

```python
class C5VersionInfo:
    """C5版本信息数据结构"""
    
    def __init__(self):
        self.version_fields = [
            "主控CPU板DSP版本号",
            "主控CPU板CPLD版本号", 
            "主控A相PWM板DSP版本号",
            "主控A相PWM板FPGA版本号",
            "主控B相PWM板DSP版本号",
            "主控B相PWM板FPGA版本号",
            "主控C相PWM板DSP版本号",
            "主控C相PWM板FPGA版本号",
            "主控COMM板DSP版本号",
            "主控COMM板CPLD版本号",
            "主控CPU板FPGA版本号",
            "主控CPU板子版本号"
        ]
        
    def parse_version_data(self, data_bytes: bytes) -> dict:
        """解析版本数据"""
        if len(data_bytes) != 48:
            raise ValueError("版本数据长度必须为48字节")
            
        versions = {}
        for i in range(12):
            start_idx = i * 4
            version_bytes = data_bytes[start_idx:start_idx + 4]
            version_value = int.from_bytes(version_bytes, byteorder='little')
            
            if version_value == 0x88888888:
                version_str = "无效"
            elif version_value == 0x00000000:
                version_str = "0.0.0.0"
            else:
                # 根据实际需要选择解析方式
                version_str = self.format_version(version_value)
                
            versions[self.version_fields[i]] = {
                "raw_value": f"0x{version_value:08X}",
                "formatted": version_str
            }
            
        return versions
```

## 6. 校验规则

### 6.1 校验算法

采用**单字节和校验**方式：

1. 将帧头到数据部分的所有字节转换为十进制
2. 计算所有字节的累加和
3. 对累加和取模256（保留低8位）
4. 结果即为校验位

### 6.2 校验示例

以查询帧为例：`EB 90 01 01 C5 00 00 00 00 00 00 00 00`

```
校验计算：
EB(235) + 90(144) + 01(1) + 01(1) + C5(197) + 00(0) + ... + 00(0) = 579
579 % 256 = 67 = 0x43
```

**注意**：示例中的校验位42可能是特定设备的校验结果，实际使用时需要按照设备要求计算。

## 7. 实现注意事项

### 7.1 字节序处理

- **重要**：版本数据采用小端字节序(Little Endian)
- 解析时需要将字节顺序反转：`[D, C, B, A] → 0xABCD`

### 7.2 版本号格式化

根据不同模块的版本号编码规则进行格式化：
- **日期格式**：如 0x20250306 → "25-03-06"
- **版本格式**：如 0x83150923 → "8.3.15.83"
- **特殊值**：0x88888888 表示无效，0x00000000 表示未设置

### 7.3 错误处理

- 检查响应帧长度是否为60字节
- 验证帧头、帧尾是否正确
- 校验位验证
- 数据长度字段验证（应为0x30）

### 7.4 数据存储建议

建议将解析后的版本信息以JSON格式存储：

```json
{
  "command": "C5",
  "timestamp": "2025-01-15 10:30:25.123",
  "versions": {
    "主控CPU板DSP版本号": {
      "raw_value": "0x83150923",
      "formatted": "8.3.15.83"
    },
    "主控CPU板CPLD版本号": {
      "raw_value": "0x20250306", 
      "formatted": "25-03-06"
    }
  }
}
```

## 8. 开发实现指导

### 8.1 解析步骤

1. **帧验证**：检查帧头(EB900101)、命令码(C5)、帧尾(AAAB)
2. **长度检查**：确认数据长度字段为0x30(48字节)
3. **数据提取**：提取48字节版本数据部分
4. **逐组解析**：按4字节一组，共12组进行解析
5. **字节序转换**：小端转大端，获取实际数值
6. **格式化输出**：根据版本编码规则格式化显示

### 8.2 测试验证

使用提供的示例数据进行测试：
- 输入：`EB 90 01 01 C5 30 00 00 00 23 09 15 83...`
- 预期输出：12组版本信息，包含有效版本和无效标识

通过以上协议解析文档，开发人员可以准确实现C5版本信息命令的数据解析功能。
