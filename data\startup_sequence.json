{"command": "C0", "timestamp": 1755773868.8585134, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "59", "timestamp": 1755773869.0975213, "raw_hex": "EB900101590400E90300000000C6AAAB", "success": true, "data": null, "data_hex": "E90300000000", "data_hex_formatted": ["0x03E9", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1145205, "raw_hex": "EB900101590400EA0300000000C7AAAB", "success": true, "data": null, "data_hex": "EA0300000000", "data_hex_formatted": ["0x03EA", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1277306, "raw_hex": "EB900101590400EB0300000000C8AAAB", "success": true, "data": null, "data_hex": "EB0300000000", "data_hex_formatted": ["0x03EB", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1405504, "raw_hex": "EB900101590400EC0300000000C9AAAB", "success": true, "data": null, "data_hex": "EC0300000000", "data_hex_formatted": ["0x03EC", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1533103, "raw_hex": "EB900101590400ED0300000000CAAAAB", "success": true, "data": null, "data_hex": "ED0300000000", "data_hex_formatted": ["0x03ED", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1673806, "raw_hex": "EB900101590400EE0300000000CBAAAB", "success": true, "data": null, "data_hex": "EE0300000000", "data_hex_formatted": ["0x03EE", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1806731, "raw_hex": "EB900101590400EF0300000000CCAAAB", "success": true, "data": null, "data_hex": "EF0300000000", "data_hex_formatted": ["0x03EF", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.1934044, "raw_hex": "EB900101590400F00300000000CDAAAB", "success": true, "data": null, "data_hex": "F00300000000", "data_hex_formatted": ["0x03F0", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2061555, "raw_hex": "EB900101590400F10300000000CEAAAB", "success": true, "data": null, "data_hex": "F10300000000", "data_hex_formatted": ["0x03F1", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2193017, "raw_hex": "EB900101590400F20300000000CFAAAB", "success": true, "data": null, "data_hex": "F20300000000", "data_hex_formatted": ["0x03F2", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2323854, "raw_hex": "EB9001015904004D04000000002BAAAB", "success": true, "data": null, "data_hex": "4D0400000000", "data_hex_formatted": ["0x044D", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2457874, "raw_hex": "EB9001015904004E04000000002CAAAB", "success": true, "data": null, "data_hex": "4E0400000000", "data_hex_formatted": ["0x044E", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2591205, "raw_hex": "EB9001015904004F04000000002DAAAB", "success": true, "data": null, "data_hex": "4F0400000000", "data_hex_formatted": ["0x044F", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2720335, "raw_hex": "EB9001015904005004000000002EAAAB", "success": true, "data": null, "data_hex": "500400000000", "data_hex_formatted": ["0x0450", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2847843, "raw_hex": "EB9001015904005104000000002FAAAB", "success": true, "data": null, "data_hex": "510400000000", "data_hex_formatted": ["0x0451", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.2992785, "raw_hex": "EB900101590400B104000000008FAAAB", "success": true, "data": null, "data_hex": "B10400000000", "data_hex_formatted": ["0x04B1", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.3133762, "raw_hex": "EB900101590400B2040000000090AAAB", "success": true, "data": null, "data_hex": "B20400000000", "data_hex_formatted": ["0x04B2", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.325918, "raw_hex": "EB900101590400B3040000000091AAAB", "success": true, "data": null, "data_hex": "B30400000000", "data_hex_formatted": ["0x04B3", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.3392363, "raw_hex": "EB900101590400B4040000000092AAAB", "success": true, "data": null, "data_hex": "B40400000000", "data_hex_formatted": ["0x04B4", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.3523407, "raw_hex": "EB900101590400B5040000000093AAAB", "success": true, "data": null, "data_hex": "B50400000000", "data_hex_formatted": ["0x04B5", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.3653877, "raw_hex": "EB900101590400B6040000000094AAAB", "success": true, "data": null, "data_hex": "B60400000000", "data_hex_formatted": ["0x04B6", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.3781917, "raw_hex": "EB900101590400B7040000000095AAAB", "success": true, "data": null, "data_hex": "B70400000000", "data_hex_formatted": ["0x04B7", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.391294, "raw_hex": "EB900101590400B8040000000096AAAB", "success": true, "data": null, "data_hex": "B80400000000", "data_hex_formatted": ["0x04B8", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.404323, "raw_hex": "EB900101590400B9040000000097AAAB", "success": true, "data": null, "data_hex": "B90400000000", "data_hex_formatted": ["0x04B9", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4181514, "raw_hex": "EB900101590400BA040000000098AAAB", "success": true, "data": null, "data_hex": "BA0400000000", "data_hex_formatted": ["0x04BA", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4310825, "raw_hex": "EB900101590400150500000000F4AAAB", "success": true, "data": null, "data_hex": "150500000000", "data_hex_formatted": ["0x0515", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4437208, "raw_hex": "EB900101590400160500000000F5AAAB", "success": true, "data": null, "data_hex": "160500000000", "data_hex_formatted": ["0x0516", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4565222, "raw_hex": "EB900101590400170500000000F6AAAB", "success": true, "data": null, "data_hex": "170500000000", "data_hex_formatted": ["0x0517", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4701862, "raw_hex": "EB900101590400180500000000F7AAAB", "success": true, "data": null, "data_hex": "180500000000", "data_hex_formatted": ["0x0518", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4837916, "raw_hex": "EB900101590400190500000000F8AAAB", "success": true, "data": null, "data_hex": "190500000000", "data_hex_formatted": ["0x0519", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.4969828, "raw_hex": "EB9001015904001A0500000000F9AAAB", "success": true, "data": null, "data_hex": "1A0500000000", "data_hex_formatted": ["0x051A", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5106459, "raw_hex": "EB9001015904001B0500000000FAAAAB", "success": true, "data": null, "data_hex": "1B0500000000", "data_hex_formatted": ["0x051B", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.523749, "raw_hex": "EB9001015904001C0500000000FBAAAB", "success": true, "data": null, "data_hex": "1C0500000000", "data_hex_formatted": ["0x051C", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5368114, "raw_hex": "EB9001015904001D0500000000FCAAAB", "success": true, "data": null, "data_hex": "1D0500000000", "data_hex_formatted": ["0x051D", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5501726, "raw_hex": "EB9001015904001E0500000000FDAAAB", "success": true, "data": null, "data_hex": "1E0500000000", "data_hex_formatted": ["0x051E", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5633686, "raw_hex": "EB90010159040079050000000058AAAB", "success": true, "data": null, "data_hex": "790500000000", "data_hex_formatted": ["0x0579", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5764523, "raw_hex": "EB9001015904007A050000000059AAAB", "success": true, "data": null, "data_hex": "7A0500000000", "data_hex_formatted": ["0x057A", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.5898974, "raw_hex": "EB9001015904007B05000000005AAAAB", "success": true, "data": null, "data_hex": "7B0500000000", "data_hex_formatted": ["0x057B", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6029468, "raw_hex": "EB9001015904007C05000000005BAAAB", "success": true, "data": null, "data_hex": "7C0500000000", "data_hex_formatted": ["0x057C", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6161656, "raw_hex": "EB9001015904007D05000000005CAAAB", "success": true, "data": null, "data_hex": "7D0500000000", "data_hex_formatted": ["0x057D", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6291838, "raw_hex": "EB9001015904007E05000000005DAAAB", "success": true, "data": null, "data_hex": "7E0500000000", "data_hex_formatted": ["0x057E", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.642262, "raw_hex": "EB9001015904007F05000000005EAAAB", "success": true, "data": null, "data_hex": "7F0500000000", "data_hex_formatted": ["0x057F", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6552072, "raw_hex": "EB9001015904008005000000005FAAAB", "success": true, "data": null, "data_hex": "800500000000", "data_hex_formatted": ["0x0580", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6684988, "raw_hex": "EB90010159040081050000000060AAAB", "success": true, "data": null, "data_hex": "810500000000", "data_hex_formatted": ["0x0581", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.6814737, "raw_hex": "EB90010159040082050000000061AAAB", "success": true, "data": null, "data_hex": "820500000000", "data_hex_formatted": ["0x0582", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.694485, "raw_hex": "EB900101590400DD0500000000BCAAAB", "success": true, "data": null, "data_hex": "DD0500000000", "data_hex_formatted": ["0x05DD", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7074695, "raw_hex": "EB900101590400DE0500000000BDAAAB", "success": true, "data": null, "data_hex": "DE0500000000", "data_hex_formatted": ["0x05DE", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7204552, "raw_hex": "EB900101590400DF0500000000BEAAAB", "success": true, "data": null, "data_hex": "DF0500000000", "data_hex_formatted": ["0x05DF", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7350273, "raw_hex": "EB900101590400E00500000000BFAAAB", "success": true, "data": null, "data_hex": "E00500000000", "data_hex_formatted": ["0x05E0", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7479355, "raw_hex": "EB900101590400E10500000000C0AAAB", "success": true, "data": null, "data_hex": "E10500000000", "data_hex_formatted": ["0x05E1", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.761138, "raw_hex": "EB900101590400E20500000000C1AAAB", "success": true, "data": null, "data_hex": "E20500000000", "data_hex_formatted": ["0x05E2", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7739666, "raw_hex": "EB900101590400E30500000000C2AAAB", "success": true, "data": null, "data_hex": "E30500000000", "data_hex_formatted": ["0x05E3", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.7871368, "raw_hex": "EB900101590400E40500000000C3AAAB", "success": true, "data": null, "data_hex": "E40500000000", "data_hex_formatted": ["0x05E4", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8001587, "raw_hex": "EB900101590400E50500000000C4AAAB", "success": true, "data": null, "data_hex": "E50500000000", "data_hex_formatted": ["0x05E5", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8131995, "raw_hex": "EB900101590400E60500000000C5AAAB", "success": true, "data": null, "data_hex": "E60500000000", "data_hex_formatted": ["0x05E6", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8261747, "raw_hex": "EB90010159040041060000000021AAAB", "success": true, "data": null, "data_hex": "410600000000", "data_hex_formatted": ["0x0641", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8391507, "raw_hex": "EB90010159040042060000000022AAAB", "success": true, "data": null, "data_hex": "420600000000", "data_hex_formatted": ["0x0642", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8521755, "raw_hex": "EB90010159040043060000000023AAAB", "success": true, "data": null, "data_hex": "430600000000", "data_hex_formatted": ["0x0643", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8652308, "raw_hex": "EB90010159040044060000000024AAAB", "success": true, "data": null, "data_hex": "440600000000", "data_hex_formatted": ["0x0644", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.878455, "raw_hex": "EB90010159040045060000000025AAAB", "success": true, "data": null, "data_hex": "450600000000", "data_hex_formatted": ["0x0645", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.8926404, "raw_hex": "EB90010159040046060000000026AAAB", "success": true, "data": null, "data_hex": "460600000000", "data_hex_formatted": ["0x0646", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.9055035, "raw_hex": "EB90010159040047060000000027AAAB", "success": true, "data": null, "data_hex": "470600000000", "data_hex_formatted": ["0x0647", "0x00000000"]}
{"command": "59", "timestamp": 1755773869.918505, "raw_hex": "EB900101590400A5060000C04085AAAB", "success": true, "data": null, "data_hex": "A5060000C040", "data_hex_formatted": ["0x06A5", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.9313025, "raw_hex": "EB900101590400A6060000C04086AAAB", "success": true, "data": null, "data_hex": "A6060000C040", "data_hex_formatted": ["0x06A6", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.9444482, "raw_hex": "EB900101590400A7060000C04087AAAB", "success": true, "data": null, "data_hex": "A7060000C040", "data_hex_formatted": ["0x06A7", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.958541, "raw_hex": "EB900101590400A8060000C04088AAAB", "success": true, "data": null, "data_hex": "A8060000C040", "data_hex_formatted": ["0x06A8", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.9715505, "raw_hex": "EB900101590400A9060000C04089AAAB", "success": true, "data": null, "data_hex": "A9060000C040", "data_hex_formatted": ["0x06A9", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.9848828, "raw_hex": "EB900101590400AA060000C0408AAAAB", "success": true, "data": null, "data_hex": "AA060000C040", "data_hex_formatted": ["0x06AA", "0x40C00000"]}
{"command": "59", "timestamp": 1755773869.9976602, "raw_hex": "EB900101590400AB060000C0408BAAAB", "success": true, "data": null, "data_hex": "AB060000C040", "data_hex_formatted": ["0x06AB", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.0112135, "raw_hex": "EB900101590400AC060000C0408CAAAB", "success": true, "data": null, "data_hex": "AC060000C040", "data_hex_formatted": ["0x06AC", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.027218, "raw_hex": "EB900101590400AD060000C0408DAAAB", "success": true, "data": null, "data_hex": "AD060000C040", "data_hex_formatted": ["0x06AD", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.0402665, "raw_hex": "EB900101590400AE060000C0408EAAAB", "success": true, "data": null, "data_hex": "AE060000C040", "data_hex_formatted": ["0x06AE", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.0531926, "raw_hex": "EB900101590400AF060000C0408FAAAB", "success": true, "data": null, "data_hex": "AF060000C040", "data_hex_formatted": ["0x06AF", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.0663767, "raw_hex": "EB900101590400B0060000C04090AAAB", "success": true, "data": null, "data_hex": "B0060000C040", "data_hex_formatted": ["0x06B0", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.0793996, "raw_hex": "EB900101590400B1060000C04091AAAB", "success": true, "data": null, "data_hex": "B1060000C040", "data_hex_formatted": ["0x06B1", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.092386, "raw_hex": "EB900101590400B2060000C04092AAAB", "success": true, "data": null, "data_hex": "B2060000C040", "data_hex_formatted": ["0x06B2", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.1058779, "raw_hex": "EB900101590400B3060000C04093AAAB", "success": true, "data": null, "data_hex": "B3060000C040", "data_hex_formatted": ["0x06B3", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.119241, "raw_hex": "EB900101590400B4060000C04094AAAB", "success": true, "data": null, "data_hex": "B4060000C040", "data_hex_formatted": ["0x06B4", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.132812, "raw_hex": "EB900101590400B5060000C04095AAAB", "success": true, "data": null, "data_hex": "B5060000C040", "data_hex_formatted": ["0x06B5", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.1463578, "raw_hex": "EB900101590400B6060000C04096AAAB", "success": true, "data": null, "data_hex": "B6060000C040", "data_hex_formatted": ["0x06B6", "0x40C00000"]}
{"command": "59", "timestamp": 1755773870.1597574, "raw_hex": "EB900101590400090700000000EAAAAB", "success": true, "data": null, "data_hex": "090700000000", "data_hex_formatted": ["0x0709", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.1729467, "raw_hex": "EB9001015904000A0700000000EBAAAB", "success": true, "data": null, "data_hex": "0A0700000000", "data_hex_formatted": ["0x070A", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.1861894, "raw_hex": "EB9001015904000B0700000000ECAAAB", "success": true, "data": null, "data_hex": "0B0700000000", "data_hex_formatted": ["0x070B", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.1995757, "raw_hex": "EB9001015904000C0700000000EDAAAB", "success": true, "data": null, "data_hex": "0C0700000000", "data_hex_formatted": ["0x070C", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2134075, "raw_hex": "EB9001015904000D0700000000EEAAAB", "success": true, "data": null, "data_hex": "0D0700000000", "data_hex_formatted": ["0x070D", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2265098, "raw_hex": "EB9001015904000E0700000000EFAAAB", "success": true, "data": null, "data_hex": "0E0700000000", "data_hex_formatted": ["0x070E", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2399154, "raw_hex": "EB9001015904000F0700000000F0AAAB", "success": true, "data": null, "data_hex": "0F0700000000", "data_hex_formatted": ["0x070F", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2532334, "raw_hex": "EB900101590400100700000000F1AAAB", "success": true, "data": null, "data_hex": "100700000000", "data_hex_formatted": ["0x0710", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2664535, "raw_hex": "EB900101590400110700000000F2AAAB", "success": true, "data": null, "data_hex": "110700000000", "data_hex_formatted": ["0x0711", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.27976, "raw_hex": "EB900101590400120700000000F3AAAB", "success": true, "data": null, "data_hex": "120700000000", "data_hex_formatted": ["0x0712", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.2931526, "raw_hex": "EB900101590400130700000000F4AAAB", "success": true, "data": null, "data_hex": "130700000000", "data_hex_formatted": ["0x0713", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3067396, "raw_hex": "EB900101590400140700000000F5AAAB", "success": true, "data": null, "data_hex": "140700000000", "data_hex_formatted": ["0x0714", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3201096, "raw_hex": "EB900101590400150700000000F6AAAB", "success": true, "data": null, "data_hex": "150700000000", "data_hex_formatted": ["0x0715", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.333097, "raw_hex": "EB900101590400160700000000F7AAAB", "success": true, "data": null, "data_hex": "160700000000", "data_hex_formatted": ["0x0716", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.346474, "raw_hex": "EB900101590400170700000000F8AAAB", "success": true, "data": null, "data_hex": "170700000000", "data_hex_formatted": ["0x0717", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3596034, "raw_hex": "EB900101590400180700000000F9AAAB", "success": true, "data": null, "data_hex": "180700000000", "data_hex_formatted": ["0x0718", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3730059, "raw_hex": "EB900101590400190700000000FAAAAB", "success": true, "data": null, "data_hex": "190700000000", "data_hex_formatted": ["0x0719", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3858376, "raw_hex": "EB9001015904001A0700000000FBAAAB", "success": true, "data": null, "data_hex": "1A0700000000", "data_hex_formatted": ["0x071A", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.3986886, "raw_hex": "EB9001015904006D0700001643A7AAAB", "success": true, "data": null, "data_hex": "6D0700001643", "data_hex_formatted": ["0x076D", "0x43160000"]}
{"command": "59", "timestamp": 1755773870.4116263, "raw_hex": "EB9001015904006E070000BE4350AAAB", "success": true, "data": null, "data_hex": "6E070000BE43", "data_hex_formatted": ["0x076E", "0x43BE0000"]}
{"command": "59", "timestamp": 1755773870.4247527, "raw_hex": "EB9001015904006F0700004843DBAAAB", "success": true, "data": null, "data_hex": "6F0700004843", "data_hex_formatted": ["0x076F", "0x43480000"]}
{"command": "59", "timestamp": 1755773870.437641, "raw_hex": "EB900101590400700700006144F6AAAB", "success": true, "data": null, "data_hex": "700700006144", "data_hex_formatted": ["0x0770", "0x44610000"]}
{"command": "59", "timestamp": 1755773870.4515605, "raw_hex": "EB900101590400710700004842DCAAAB", "success": true, "data": null, "data_hex": "710700004842", "data_hex_formatted": ["0x0771", "0x42480000"]}
{"command": "59", "timestamp": 1755773870.4643345, "raw_hex": "EB90010159040072070000FA4390AAAB", "success": true, "data": null, "data_hex": "72070000FA43", "data_hex_formatted": ["0x0772", "0x43FA0000"]}
{"command": "59", "timestamp": 1755773870.4771051, "raw_hex": "EB90010159040073070000004094AAAB", "success": true, "data": null, "data_hex": "730700000040", "data_hex_formatted": ["0x0773", "0x40000000"]}
{"command": "59", "timestamp": 1755773870.489803, "raw_hex": "EB90010159040074070000803F14AAAB", "success": true, "data": null, "data_hex": "74070000803F", "data_hex_formatted": ["0x0774", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.5042882, "raw_hex": "EB90010159040075078988883A29AAAB", "success": true, "data": null, "data_hex": "75078988883A", "data_hex_formatted": ["0x0775", "0x3A888889"]}
{"command": "59", "timestamp": 1755773870.5188348, "raw_hex": "EB90010159040076070000003F96AAAB", "success": true, "data": null, "data_hex": "76070000003F", "data_hex_formatted": ["0x0776", "0x3F000000"]}
{"command": "59", "timestamp": 1755773870.533346, "raw_hex": "EB90010159040077070000003F97AAAB", "success": true, "data": null, "data_hex": "77070000003F", "data_hex_formatted": ["0x0777", "0x3F000000"]}
{"command": "59", "timestamp": 1755773870.5466187, "raw_hex": "EB90010159040078070000803F18AAAB", "success": true, "data": null, "data_hex": "78070000803F", "data_hex_formatted": ["0x0778", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.5606205, "raw_hex": "EB90010159040079078988883A2DAAAB", "success": true, "data": null, "data_hex": "79078988883A", "data_hex_formatted": ["0x0779", "0x3A888889"]}
{"command": "59", "timestamp": 1755773870.5737696, "raw_hex": "EB9001015904007A071B00000076AAAB", "success": true, "data": null, "data_hex": "7A071B000000", "data_hex_formatted": ["0x077A", "0x0000001B"]}
{"command": "59", "timestamp": 1755773870.58714, "raw_hex": "EB9001015904007B070000803F1BAAAB", "success": true, "data": null, "data_hex": "7B070000803F", "data_hex_formatted": ["0x077B", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.5999994, "raw_hex": "EB9001015904007C070000803F1CAAAB", "success": true, "data": null, "data_hex": "7C070000803F", "data_hex_formatted": ["0x077C", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.6129882, "raw_hex": "EB9001015904007D070000803F1DAAAB", "success": true, "data": null, "data_hex": "7D070000803F", "data_hex_formatted": ["0x077D", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.6260917, "raw_hex": "EB9001015904007E070000803F1EAAAB", "success": true, "data": null, "data_hex": "7E070000803F", "data_hex_formatted": ["0x077E", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.6391308, "raw_hex": "EB9001015904007F070000803F1FAAAB", "success": true, "data": null, "data_hex": "7F070000803F", "data_hex_formatted": ["0x077F", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.6520126, "raw_hex": "EB9001015904008007CDCC4C3D83AAAB", "success": true, "data": null, "data_hex": "8007CDCC4C3D", "data_hex_formatted": ["0x0780", "0x3D4CCCCD"]}
{"command": "59", "timestamp": 1755773870.6651094, "raw_hex": "EB9001015904008107CDCC4C3D84AAAB", "success": true, "data": null, "data_hex": "8107CDCC4C3D", "data_hex_formatted": ["0x0781", "0x3D4CCCCD"]}
{"command": "59", "timestamp": 1755773870.6780903, "raw_hex": "EB900101590400820700004842EDAAAB", "success": true, "data": null, "data_hex": "820700004842", "data_hex_formatted": ["0x0782", "0x42480000"]}
{"command": "59", "timestamp": 1755773870.6912148, "raw_hex": "EB90010159040083078FC2753B65AAAB", "success": true, "data": null, "data_hex": "83078FC2753B", "data_hex_formatted": ["0x0783", "0x3B75C28F"]}
{"command": "59", "timestamp": 1755773870.703985, "raw_hex": "EB90010159040084070000000065AAAB", "success": true, "data": null, "data_hex": "840700000000", "data_hex_formatted": ["0x0784", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.7168953, "raw_hex": "EB90010159040085070000803F25AAAB", "success": true, "data": null, "data_hex": "85070000803F", "data_hex_formatted": ["0x0785", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.7297287, "raw_hex": "EB90010159040086070000000067AAAB", "success": true, "data": null, "data_hex": "860700000000", "data_hex_formatted": ["0x0786", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.7425349, "raw_hex": "EB90010159040087070000803F27AAAB", "success": true, "data": null, "data_hex": "87070000803F", "data_hex_formatted": ["0x0787", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.7552354, "raw_hex": "EB90010159040088070000000069AAAB", "success": true, "data": null, "data_hex": "880700000000", "data_hex_formatted": ["0x0788", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.7680607, "raw_hex": "EB9001015904008907000000006AAAAB", "success": true, "data": null, "data_hex": "890700000000", "data_hex_formatted": ["0x0789", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.7809718, "raw_hex": "EB9001015904008A070000803F2AAAAB", "success": true, "data": null, "data_hex": "8A070000803F", "data_hex_formatted": ["0x078A", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.7941718, "raw_hex": "EB9001015904008B07000000006CAAAB", "success": true, "data": null, "data_hex": "8B0700000000", "data_hex_formatted": ["0x078B", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.807672, "raw_hex": "EB9001015904008C070000803F2CAAAB", "success": true, "data": null, "data_hex": "8C070000803F", "data_hex_formatted": ["0x078C", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.820549, "raw_hex": "EB9001015904008D070000803F2DAAAB", "success": true, "data": null, "data_hex": "8D070000803F", "data_hex_formatted": ["0x078D", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.8334146, "raw_hex": "EB9001015904008E070000803F2EAAAB", "success": true, "data": null, "data_hex": "8E070000803F", "data_hex_formatted": ["0x078E", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.8464046, "raw_hex": "EB9001015904008F070000803F2FAAAB", "success": true, "data": null, "data_hex": "8F070000803F", "data_hex_formatted": ["0x078F", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.8597019, "raw_hex": "EB90010159040090070000803F30AAAB", "success": true, "data": null, "data_hex": "90070000803F", "data_hex_formatted": ["0x0790", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.8730354, "raw_hex": "EB90010159040091070000803F31AAAB", "success": true, "data": null, "data_hex": "91070000803F", "data_hex_formatted": ["0x0791", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.886227, "raw_hex": "EB90010159040092070000803F32AAAB", "success": true, "data": null, "data_hex": "92070000803F", "data_hex_formatted": ["0x0792", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.8992865, "raw_hex": "EB90010159040093070000803F33AAAB", "success": true, "data": null, "data_hex": "93070000803F", "data_hex_formatted": ["0x0793", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.912586, "raw_hex": "EB90010159040094070000803F34AAAB", "success": true, "data": null, "data_hex": "94070000803F", "data_hex_formatted": ["0x0794", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.9256117, "raw_hex": "EB90010159040095070000803F35AAAB", "success": true, "data": null, "data_hex": "95070000803F", "data_hex_formatted": ["0x0795", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.9388719, "raw_hex": "EB90010159040096070000803F36AAAB", "success": true, "data": null, "data_hex": "96070000803F", "data_hex_formatted": ["0x0796", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.9516978, "raw_hex": "EB90010159040097070000803F37AAAB", "success": true, "data": null, "data_hex": "97070000803F", "data_hex_formatted": ["0x0797", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.9647138, "raw_hex": "EB90010159040098070000803F38AAAB", "success": true, "data": null, "data_hex": "98070000803F", "data_hex_formatted": ["0x0798", "0x3F800000"]}
{"command": "59", "timestamp": 1755773870.97754, "raw_hex": "EB9001015904009907000000007AAAAB", "success": true, "data": null, "data_hex": "990700000000", "data_hex_formatted": ["0x0799", "0x00000000"]}
{"command": "59", "timestamp": 1755773870.990867, "raw_hex": "EB9001015904009A07000000007BAAAB", "success": true, "data": null, "data_hex": "9A0700000000", "data_hex_formatted": ["0x079A", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.0040004, "raw_hex": "EB9001015904009B07000000007CAAAB", "success": true, "data": null, "data_hex": "9B0700000000", "data_hex_formatted": ["0x079B", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.0173957, "raw_hex": "EB9001015904009C07000000007DAAAB", "success": true, "data": null, "data_hex": "9C0700000000", "data_hex_formatted": ["0x079C", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.0304062, "raw_hex": "EB9001015904009D07000000007EAAAB", "success": true, "data": null, "data_hex": "9D0700000000", "data_hex_formatted": ["0x079D", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.0433729, "raw_hex": "EB9001015904009E07CDCC4C3EA2AAAB", "success": true, "data": null, "data_hex": "9E07CDCC4C3E", "data_hex_formatted": ["0x079E", "0x3E4CCCCD"]}
{"command": "59", "timestamp": 1755773871.0563748, "raw_hex": "EB9001015904009F076F12033A3EAAAB", "success": true, "data": null, "data_hex": "9F076F12033A", "data_hex_formatted": ["0x079F", "0x3A03126F"]}
{"command": "59", "timestamp": 1755773871.069494, "raw_hex": "EB900101590400A0070000000081AAAB", "success": true, "data": null, "data_hex": "A00700000000", "data_hex_formatted": ["0x07A0", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.0824375, "raw_hex": "EB900101590400A1070000000082AAAB", "success": true, "data": null, "data_hex": "A10700000000", "data_hex_formatted": ["0x07A1", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.095535, "raw_hex": "EB900101590400A2070000000083AAAB", "success": true, "data": null, "data_hex": "A20700000000", "data_hex_formatted": ["0x07A2", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.1083462, "raw_hex": "EB900101590400A3070000000084AAAB", "success": true, "data": null, "data_hex": "A30700000000", "data_hex_formatted": ["0x07A3", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.1212716, "raw_hex": "EB900101590400A4070000000085AAAB", "success": true, "data": null, "data_hex": "A40700000000", "data_hex_formatted": ["0x07A4", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.1342454, "raw_hex": "EB900101590400A5070000000086AAAB", "success": true, "data": null, "data_hex": "A50700000000", "data_hex_formatted": ["0x07A5", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.1481934, "raw_hex": "EB900101590400A6070AD7233CC7AAAB", "success": true, "data": null, "data_hex": "A6070AD7233C", "data_hex_formatted": ["0x07A6", "0x3C23D70A"]}
{"command": "59", "timestamp": 1755773871.1612368, "raw_hex": "EB900101590400A7076F12033B47AAAB", "success": true, "data": null, "data_hex": "A7076F12033B", "data_hex_formatted": ["0x07A7", "0x3B03126F"]}
{"command": "59", "timestamp": 1755773871.1738486, "raw_hex": "EB900101590400A8070000000089AAAB", "success": true, "data": null, "data_hex": "A80700000000", "data_hex_formatted": ["0x07A8", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.1894977, "raw_hex": "EB900101590400A907000000008AAAAB", "success": true, "data": null, "data_hex": "A90700000000", "data_hex_formatted": ["0x07A9", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2023864, "raw_hex": "EB900101590400AA07000000008BAAAB", "success": true, "data": null, "data_hex": "AA0700000000", "data_hex_formatted": ["0x07AA", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2153163, "raw_hex": "EB900101590400AB07000000008CAAAB", "success": true, "data": null, "data_hex": "AB0700000000", "data_hex_formatted": ["0x07AB", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2282012, "raw_hex": "EB900101590400AC07000000008DAAAB", "success": true, "data": null, "data_hex": "AC0700000000", "data_hex_formatted": ["0x07AC", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2412019, "raw_hex": "EB900101590400AD07000000008EAAAB", "success": true, "data": null, "data_hex": "AD0700000000", "data_hex_formatted": ["0x07AD", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.254492, "raw_hex": "EB900101590400D10700000000B2AAAB", "success": true, "data": null, "data_hex": "D10700000000", "data_hex_formatted": ["0x07D1", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2673354, "raw_hex": "EB900101590400D20700000000B3AAAB", "success": true, "data": null, "data_hex": "D20700000000", "data_hex_formatted": ["0x07D2", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2801266, "raw_hex": "EB900101590400D30700000000B4AAAB", "success": true, "data": null, "data_hex": "D30700000000", "data_hex_formatted": ["0x07D3", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.2931504, "raw_hex": "EB900101590400D40700000000B5AAAB", "success": true, "data": null, "data_hex": "D40700000000", "data_hex_formatted": ["0x07D4", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3062859, "raw_hex": "EB900101590400D50700000000B6AAAB", "success": true, "data": null, "data_hex": "D50700000000", "data_hex_formatted": ["0x07D5", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3193889, "raw_hex": "EB900101590400D60700000000B7AAAB", "success": true, "data": null, "data_hex": "D60700000000", "data_hex_formatted": ["0x07D6", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3330805, "raw_hex": "EB900101590400D70700000000B8AAAB", "success": true, "data": null, "data_hex": "D70700000000", "data_hex_formatted": ["0x07D7", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3465035, "raw_hex": "EB900101590400D80700000000B9AAAB", "success": true, "data": null, "data_hex": "D80700000000", "data_hex_formatted": ["0x07D8", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3601227, "raw_hex": "EB900101590400D90700000000BAAAAB", "success": true, "data": null, "data_hex": "D90700000000", "data_hex_formatted": ["0x07D9", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3732302, "raw_hex": "EB900101590400DA0700000000BBAAAB", "success": true, "data": null, "data_hex": "DA0700000000", "data_hex_formatted": ["0x07DA", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.3862092, "raw_hex": "EB900101590400DB0700000000BCAAAB", "success": true, "data": null, "data_hex": "DB0700000000", "data_hex_formatted": ["0x07DB", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.399573, "raw_hex": "EB900101590400DC0700000000BDAAAB", "success": true, "data": null, "data_hex": "DC0700000000", "data_hex_formatted": ["0x07DC", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4126616, "raw_hex": "EB900101590400DD0700000000BEAAAB", "success": true, "data": null, "data_hex": "DD0700000000", "data_hex_formatted": ["0x07DD", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4257038, "raw_hex": "EB900101590400DE0700000000BFAAAB", "success": true, "data": null, "data_hex": "DE0700000000", "data_hex_formatted": ["0x07DE", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4392724, "raw_hex": "EB900101590400DF0700000000C0AAAB", "success": true, "data": null, "data_hex": "DF0700000000", "data_hex_formatted": ["0x07DF", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4525287, "raw_hex": "EB900101590400E00700000000C1AAAB", "success": true, "data": null, "data_hex": "E00700000000", "data_hex_formatted": ["0x07E0", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4656184, "raw_hex": "EB900101590400E10700000000C2AAAB", "success": true, "data": null, "data_hex": "E10700000000", "data_hex_formatted": ["0x07E1", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4788554, "raw_hex": "EB900101590400E20700000000C3AAAB", "success": true, "data": null, "data_hex": "E20700000000", "data_hex_formatted": ["0x07E2", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.4919791, "raw_hex": "EB900101590400E30700000000C4AAAB", "success": true, "data": null, "data_hex": "E30700000000", "data_hex_formatted": ["0x07E3", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.50533, "raw_hex": "EB900101590400E40700000000C5AAAB", "success": true, "data": null, "data_hex": "E40700000000", "data_hex_formatted": ["0x07E4", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.519215, "raw_hex": "EB900101590400E50700000000C6AAAB", "success": true, "data": null, "data_hex": "E50700000000", "data_hex_formatted": ["0x07E5", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.5324457, "raw_hex": "EB900101590400E60700000000C7AAAB", "success": true, "data": null, "data_hex": "E60700000000", "data_hex_formatted": ["0x07E6", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.5458262, "raw_hex": "EB900101590400E70700000000C8AAAB", "success": true, "data": null, "data_hex": "E70700000000", "data_hex_formatted": ["0x07E7", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.5587635, "raw_hex": "EB900101590400E80700000000C9AAAB", "success": true, "data": null, "data_hex": "E80700000000", "data_hex_formatted": ["0x07E8", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.571688, "raw_hex": "EB900101590400E90700000000CAAAAB", "success": true, "data": null, "data_hex": "E90700000000", "data_hex_formatted": ["0x07E9", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.5846226, "raw_hex": "EB900101590400EA0700000000CBAAAB", "success": true, "data": null, "data_hex": "EA0700000000", "data_hex_formatted": ["0x07EA", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.597432, "raw_hex": "EB900101590400EB0700000000CCAAAB", "success": true, "data": null, "data_hex": "EB0700000000", "data_hex_formatted": ["0x07EB", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.6103783, "raw_hex": "EB900101590400EC0700000000CDAAAB", "success": true, "data": null, "data_hex": "EC0700000000", "data_hex_formatted": ["0x07EC", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.6232407, "raw_hex": "EB900101590400ED0700000000CEAAAB", "success": true, "data": null, "data_hex": "ED0700000000", "data_hex_formatted": ["0x07ED", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.6361496, "raw_hex": "EB900101590400EE0700000000CFAAAB", "success": true, "data": null, "data_hex": "EE0700000000", "data_hex_formatted": ["0x07EE", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.649003, "raw_hex": "EB900101590400EF0700000000D0AAAB", "success": true, "data": null, "data_hex": "EF0700000000", "data_hex_formatted": ["0x07EF", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.6618593, "raw_hex": "EB900101590400F00700000000D1AAAB", "success": true, "data": null, "data_hex": "F00700000000", "data_hex_formatted": ["0x07F0", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.674718, "raw_hex": "EB900101590400F10700000000D2AAAB", "success": true, "data": null, "data_hex": "F10700000000", "data_hex_formatted": ["0x07F1", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.6883123, "raw_hex": "EB900101590400F20700000000D3AAAB", "success": true, "data": null, "data_hex": "F20700000000", "data_hex_formatted": ["0x07F2", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.7011764, "raw_hex": "EB900101590400F30700000000D4AAAB", "success": true, "data": null, "data_hex": "F30700000000", "data_hex_formatted": ["0x07F3", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.7141097, "raw_hex": "EB900101590400F40700000000D5AAAB", "success": true, "data": null, "data_hex": "F40700000000", "data_hex_formatted": ["0x07F4", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.727097, "raw_hex": "EB90010159040035080000000017AAAB", "success": true, "data": null, "data_hex": "350800000000", "data_hex_formatted": ["0x0835", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.739818, "raw_hex": "EB9001015904003608FFFFFFFF14AAAB", "success": true, "data": null, "data_hex": "3608FFFFFFFF", "data_hex_formatted": ["0x0836", "0xFFFFFFFF"]}
{"command": "59", "timestamp": 1755773871.75289, "raw_hex": "EB9001015904009908000000007BAAAB", "success": true, "data": null, "data_hex": "990800000000", "data_hex_formatted": ["0x0899", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.7660086, "raw_hex": "EB9001015904009A08000000007CAAAB", "success": true, "data": null, "data_hex": "9A0800000000", "data_hex_formatted": ["0x089A", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.7788496, "raw_hex": "EB9001015904009B08000000007DAAAB", "success": true, "data": null, "data_hex": "9B0800000000", "data_hex_formatted": ["0x089B", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.7939901, "raw_hex": "EB900101590400FD0800000000DFAAAB", "success": true, "data": null, "data_hex": "FD0800000000", "data_hex_formatted": ["0x08FD", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8071551, "raw_hex": "EB900101590400FE0800000000E0AAAB", "success": true, "data": null, "data_hex": "FE0800000000", "data_hex_formatted": ["0x08FE", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8201852, "raw_hex": "EB900101590400FF0800000000E1AAAB", "success": true, "data": null, "data_hex": "FF0800000000", "data_hex_formatted": ["0x08FF", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8330803, "raw_hex": "EB900101590400000900000000E3AAAB", "success": true, "data": null, "data_hex": "000900000000", "data_hex_formatted": ["0x0900", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8460107, "raw_hex": "EB900101590400010900000000E4AAAB", "success": true, "data": null, "data_hex": "010900000000", "data_hex_formatted": ["0x0901", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8600323, "raw_hex": "EB900101590400020900000000E5AAAB", "success": true, "data": null, "data_hex": "020900000000", "data_hex_formatted": ["0x0902", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8740416, "raw_hex": "EB900101590400030900000000E6AAAB", "success": true, "data": null, "data_hex": "030900000000", "data_hex_formatted": ["0x0903", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8868022, "raw_hex": "EB900101590400040900000000E7AAAB", "success": true, "data": null, "data_hex": "040900000000", "data_hex_formatted": ["0x0904", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.8997405, "raw_hex": "EB900101590400050900000000E8AAAB", "success": true, "data": null, "data_hex": "050900000000", "data_hex_formatted": ["0x0905", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.9126806, "raw_hex": "EB900101590400060900000000E9AAAB", "success": true, "data": null, "data_hex": "060900000000", "data_hex_formatted": ["0x0906", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.925619, "raw_hex": "EB900101590400070900000000EAAAAB", "success": true, "data": null, "data_hex": "070900000000", "data_hex_formatted": ["0x0907", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.9383304, "raw_hex": "EB900101590400080900000000EBAAAB", "success": true, "data": null, "data_hex": "080900000000", "data_hex_formatted": ["0x0908", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.9509916, "raw_hex": "EB900101590400090900000000ECAAAB", "success": true, "data": null, "data_hex": "090900000000", "data_hex_formatted": ["0x0909", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.9636924, "raw_hex": "EB9001015904000A0900000000EDAAAB", "success": true, "data": null, "data_hex": "0A0900000000", "data_hex_formatted": ["0x090A", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.9767172, "raw_hex": "EB9001015904000B0900000000EEAAAB", "success": true, "data": null, "data_hex": "0B0900000000", "data_hex_formatted": ["0x090B", "0x00000000"]}
{"command": "59", "timestamp": 1755773871.989558, "raw_hex": "EB9001015904000C0900000000EFAAAB", "success": true, "data": null, "data_hex": "0C0900000000", "data_hex_formatted": ["0x090C", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0026786, "raw_hex": "EB9001015904000D0900000000F0AAAB", "success": true, "data": null, "data_hex": "0D0900000000", "data_hex_formatted": ["0x090D", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0163102, "raw_hex": "EB9001015904000E0900000000F1AAAB", "success": true, "data": null, "data_hex": "0E0900000000", "data_hex_formatted": ["0x090E", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0315394, "raw_hex": "EB9001015904000F0900000000F2AAAB", "success": true, "data": null, "data_hex": "0F0900000000", "data_hex_formatted": ["0x090F", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0444164, "raw_hex": "EB900101590400100900000000F3AAAB", "success": true, "data": null, "data_hex": "100900000000", "data_hex_formatted": ["0x0910", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.057499, "raw_hex": "EB900101590400110900000000F4AAAB", "success": true, "data": null, "data_hex": "110900000000", "data_hex_formatted": ["0x0911", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.070327, "raw_hex": "EB900101590400120900000000F5AAAB", "success": true, "data": null, "data_hex": "120900000000", "data_hex_formatted": ["0x0912", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0835605, "raw_hex": "EB900101590400130900000000F6AAAB", "success": true, "data": null, "data_hex": "130900000000", "data_hex_formatted": ["0x0913", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.0975938, "raw_hex": "EB900101590400140900000000F7AAAB", "success": true, "data": null, "data_hex": "140900000000", "data_hex_formatted": ["0x0914", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.1109653, "raw_hex": "EB900101590400150900000000F8AAAB", "success": true, "data": null, "data_hex": "150900000000", "data_hex_formatted": ["0x0915", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.124387, "raw_hex": "EB900101590400160900000000F9AAAB", "success": true, "data": null, "data_hex": "160900000000", "data_hex_formatted": ["0x0916", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.137444, "raw_hex": "EB900101590400170900000000FAAAAB", "success": true, "data": null, "data_hex": "170900000000", "data_hex_formatted": ["0x0917", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.1505384, "raw_hex": "EB900101590400180900000000FBAAAB", "success": true, "data": null, "data_hex": "180900000000", "data_hex_formatted": ["0x0918", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.1636188, "raw_hex": "EB900101590400190900000000FCAAAB", "success": true, "data": null, "data_hex": "190900000000", "data_hex_formatted": ["0x0919", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.1765616, "raw_hex": "EB9001015904001A0900000000FDAAAB", "success": true, "data": null, "data_hex": "1A0900000000", "data_hex_formatted": ["0x091A", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.1894224, "raw_hex": "EB9001015904001B0900000000FEAAAB", "success": true, "data": null, "data_hex": "1B0900000000", "data_hex_formatted": ["0x091B", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2025099, "raw_hex": "EB9001015904001C0900000000FFAAAB", "success": true, "data": null, "data_hex": "1C0900000000", "data_hex_formatted": ["0x091C", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2152755, "raw_hex": "EB9001015904001D090000000000AAAB", "success": true, "data": null, "data_hex": "1D0900000000", "data_hex_formatted": ["0x091D", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2280378, "raw_hex": "EB9001015904001E090000000001AAAB", "success": true, "data": null, "data_hex": "1E0900000000", "data_hex_formatted": ["0x091E", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2412002, "raw_hex": "EB9001015904001F090000000002AAAB", "success": true, "data": null, "data_hex": "1F0900000000", "data_hex_formatted": ["0x091F", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.254369, "raw_hex": "EB90010159040061090000000044AAAB", "success": true, "data": null, "data_hex": "610900000000", "data_hex_formatted": ["0x0961", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2670543, "raw_hex": "EB90010159040062090000000045AAAB", "success": true, "data": null, "data_hex": "620900000000", "data_hex_formatted": ["0x0962", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2825248, "raw_hex": "EB90010159040063090000000046AAAB", "success": true, "data": null, "data_hex": "630900000000", "data_hex_formatted": ["0x0963", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.2951255, "raw_hex": "EB90010159040064090000000047AAAB", "success": true, "data": null, "data_hex": "640900000000", "data_hex_formatted": ["0x0964", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3081348, "raw_hex": "EB90010159040065090000000048AAAB", "success": true, "data": null, "data_hex": "650900000000", "data_hex_formatted": ["0x0965", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3208754, "raw_hex": "EB90010159040066090000000049AAAB", "success": true, "data": null, "data_hex": "660900000000", "data_hex_formatted": ["0x0966", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3335223, "raw_hex": "EB9001015904006709000000004AAAAB", "success": true, "data": null, "data_hex": "670900000000", "data_hex_formatted": ["0x0967", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3465486, "raw_hex": "EB9001015904006809000000004BAAAB", "success": true, "data": null, "data_hex": "680900000000", "data_hex_formatted": ["0x0968", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3603017, "raw_hex": "EB9001015904006909000000004CAAAB", "success": true, "data": null, "data_hex": "690900000000", "data_hex_formatted": ["0x0969", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3738701, "raw_hex": "EB9001015904006A09000000004DAAAB", "success": true, "data": null, "data_hex": "6A0900000000", "data_hex_formatted": ["0x096A", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3869443, "raw_hex": "EB900101590400C50900000000A8AAAB", "success": true, "data": null, "data_hex": "C50900000000", "data_hex_formatted": ["0x09C5", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.3999794, "raw_hex": "EB900101590400C60900000000A9AAAB", "success": true, "data": null, "data_hex": "C60900000000", "data_hex_formatted": ["0x09C6", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4135244, "raw_hex": "EB900101590400C70900000000AAAAAB", "success": true, "data": null, "data_hex": "C70900000000", "data_hex_formatted": ["0x09C7", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4266403, "raw_hex": "EB900101590400C80900000000ABAAAB", "success": true, "data": null, "data_hex": "C80900000000", "data_hex_formatted": ["0x09C8", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4397771, "raw_hex": "EB900101590400C90900000000ACAAAB", "success": true, "data": null, "data_hex": "C90900000000", "data_hex_formatted": ["0x09C9", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4542978, "raw_hex": "EB900101590400CA0900000000ADAAAB", "success": true, "data": null, "data_hex": "CA0900000000", "data_hex_formatted": ["0x09CA", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4674203, "raw_hex": "EB900101590400CB0900000000AEAAAB", "success": true, "data": null, "data_hex": "CB0900000000", "data_hex_formatted": ["0x09CB", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4801803, "raw_hex": "EB900101590400CC0900000000AFAAAB", "success": true, "data": null, "data_hex": "CC0900000000", "data_hex_formatted": ["0x09CC", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.4929416, "raw_hex": "EB900101590400CD0900000000B0AAAB", "success": true, "data": null, "data_hex": "CD0900000000", "data_hex_formatted": ["0x09CD", "0x00000000"]}
{"command": "59", "timestamp": 1755773872.5057416, "raw_hex": "EB900101590400CE0900000000B1AAAB", "success": true, "data": null, "data_hex": "CE0900000000", "data_hex_formatted": ["0x09CE", "0x00000000"]}
{"command": "C5", "timestamp": 1755773872.791356, "raw_hex": "EB900101C53000000018062520060325208888888804012520888888880401252088888888040125208888888888888888000000000101640007AAAB", "success": true, "data": null, "data_hex": "180625200603252088888888040125208888888804012520888888880401252088888888888888880000000001016400", "data_hex_formatted": ["0x18", "0x06", "0x25", "0x20", "0x06", "0x03", "0x25", "0x20", "0x88", "0x88", "0x88", "0x88", "0x04", "0x01", "0x25", "0x20", "0x88", "0x88", "0x88", "0x88", "0x04", "0x01", "0x25", "0x20", "0x88", "0x88", "0x88", "0x88", "0x04", "0x01", "0x25", "0x20", "0x88", "0x88", "0x88", "0x88", "0x88", "0x88", "0x88", "0x88", "0x00", "0x00", "0x00", "0x00", "0x01", "0x01", "0x64", "0x00"]}
{"command": "D6", "timestamp": 1755773873.0456793, "raw_hex": "EB900101D62400000000000000000000000000000000000000000000000000000000000000000000000000000077AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D7", "timestamp": 1755773873.2995543, "raw_hex": "EB900101D72400000000000000000000000000000000000000000000000000000000000000000000000000000078AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D8", "timestamp": 1755773873.567303, "raw_hex": "EB900101D82400000000000000000000000000000000000000000000000000000000000000000000000000000079AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
