[{"command": "C0", "timestamp": 1756095954.7766533, "raw_hex": "EB900101C00800000000000000161000006BAAAB", "success": true, "data": null, "data_hex": "0000000016100000", "data_hex_formatted": ["0x00000000", "0x00001016"]}, {"command": "59", "channel": 1001, "timestamp": 1756091455.968897, "raw_hex": "EB900101590400E9030000803F85AAAB", "success": true, "data": null, "data_hex": "E9030000803F", "data_hex_formatted": ["0x03E9", "0x3F800000"]}, {"command": "59", "channel": 1002, "timestamp": 1756091455.9860036, "raw_hex": "EB900101590400EA0300000000C7AAAB", "success": true, "data": null, "data_hex": "EA0300000000", "data_hex_formatted": ["0x03EA", "0x00000000"]}, {"command": "59", "channel": 1003, "timestamp": 1756091456.0176592, "raw_hex": "EB900101590400EB036F12833A06AAAB", "success": true, "data": null, "data_hex": "EB036F12833A", "data_hex_formatted": ["0x03EB", "0x3A83126F"]}, {"command": "59", "channel": 1004, "timestamp": 1756091456.0478232, "raw_hex": "EB900101590400EC0300000000C9AAAB", "success": true, "data": null, "data_hex": "EC0300000000", "data_hex_formatted": ["0x03EC", "0x00000000"]}, {"command": "59", "channel": 1005, "timestamp": 1756091456.0778763, "raw_hex": "EB900101590400ED03CDCC4C3EEDAAAB", "success": true, "data": null, "data_hex": "ED03CDCC4C3E", "data_hex_formatted": ["0x03ED", "0x3E4CCCCD"]}, {"command": "59", "channel": 1006, "timestamp": 1756091456.1086357, "raw_hex": "EB900101590400EE03CDCC4CBE6EAAAB", "success": true, "data": null, "data_hex": "EE03CDCC4CBE", "data_hex_formatted": ["0x03EE", "0xBE4CCCCD"]}, {"command": "59", "channel": 1007, "timestamp": 1756091456.139726, "raw_hex": "EB900101590400EF03CDCC4C3EEFAAAB", "success": true, "data": null, "data_hex": "EF03CDCC4C3E", "data_hex_formatted": ["0x03EF", "0x3E4CCCCD"]}, {"command": "59", "channel": 1008, "timestamp": 1756091456.1707814, "raw_hex": "EB900101590400F003CDCC4CBE70AAAB", "success": true, "data": null, "data_hex": "F003CDCC4CBE", "data_hex_formatted": ["0x03F0", "0xBE4CCCCD"]}, {"command": "59", "channel": 1009, "timestamp": 1756091456.2015593, "raw_hex": "EB900101590400F10300000000CEAAAB", "success": true, "data": null, "data_hex": "F10300000000", "data_hex_formatted": ["0x03F1", "0x00000000"]}, {"command": "59", "channel": 1010, "timestamp": 1756091456.2323706, "raw_hex": "EB900101590400F20300000000CFAAAB", "success": true, "data": null, "data_hex": "F20300000000", "data_hex_formatted": ["0x03F2", "0x00000000"]}, {"command": "59", "channel": 1101, "timestamp": 1756091456.263398, "raw_hex": "EB9001015904004D049A99193FB6AAAB", "success": true, "data": null, "data_hex": "4D049A99193F", "data_hex_formatted": ["0x044D", "0x3F19999A"]}, {"command": "59", "channel": 1102, "timestamp": 1756091456.2933347, "raw_hex": "EB9001015904004E04000000002CAAAB", "success": true, "data": null, "data_hex": "4E0400000000", "data_hex_formatted": ["0x044E", "0x00000000"]}, {"command": "59", "channel": 1103, "timestamp": 1756091456.3234591, "raw_hex": "EB9001015904004F043333733F45AAAB", "success": true, "data": null, "data_hex": "4F043333733F", "data_hex_formatted": ["0x044F", "0x3F733333"]}, {"command": "59", "channel": 1104, "timestamp": 1756091456.354321, "raw_hex": "EB9001015904005004000000002EAAAB", "success": true, "data": null, "data_hex": "500400000000", "data_hex_formatted": ["0x0450", "0x00000000"]}, {"command": "59", "channel": 1105, "timestamp": 1756091456.3850763, "raw_hex": "EB9001015904005104000000002FAAAB", "success": true, "data": null, "data_hex": "510400000000", "data_hex_formatted": ["0x0451", "0x00000000"]}, {"command": "59", "channel": 1201, "timestamp": 1756091456.4161925, "raw_hex": "EB900101590400B1040000803E4DAAAB", "success": true, "data": null, "data_hex": "B1040000803E", "data_hex_formatted": ["0x04B1", "0x3E800000"]}, {"command": "59", "channel": 1202, "timestamp": 1756091456.447075, "raw_hex": "EB900101590400B2040000000090AAAB", "success": true, "data": null, "data_hex": "B20400000000", "data_hex_formatted": ["0x04B2", "0x00000000"]}, {"command": "59", "channel": 1203, "timestamp": 1756091456.4784458, "raw_hex": "EB900101590400B304A69B443B51AAAB", "success": true, "data": null, "data_hex": "B304A69B443B", "data_hex_formatted": ["0x04B3", "0x3B449BA6"]}, {"command": "59", "channel": 1204, "timestamp": 1756091456.509253, "raw_hex": "EB900101590400B4040000000092AAAB", "success": true, "data": null, "data_hex": "B40400000000", "data_hex_formatted": ["0x04B4", "0x00000000"]}, {"command": "59", "channel": 1205, "timestamp": 1756091456.5403528, "raw_hex": "EB900101590400B504CDCCCC3E36AAAB", "success": true, "data": null, "data_hex": "B504CDCCCC3E", "data_hex_formatted": ["0x04B5", "0x3ECCCCCD"]}, {"command": "59", "channel": 1206, "timestamp": 1756091456.5721686, "raw_hex": "EB900101590400B604CDCCCCBEB7AAAB", "success": true, "data": null, "data_hex": "B604CDCCCCBE", "data_hex_formatted": ["0x04B6", "0xBECCCCCD"]}, {"command": "59", "channel": 1207, "timestamp": 1756091456.6029003, "raw_hex": "EB900101590400B704CDCCCC3E38AAAB", "success": true, "data": null, "data_hex": "B704CDCCCC3E", "data_hex_formatted": ["0x04B7", "0x3ECCCCCD"]}, {"command": "59", "channel": 1208, "timestamp": 1756091456.6330025, "raw_hex": "EB900101590400B804CDCCCCBEB9AAAB", "success": true, "data": null, "data_hex": "B804CDCCCCBE", "data_hex_formatted": ["0x04B8", "0xBECCCCCD"]}, {"command": "59", "channel": 1209, "timestamp": 1756091456.6645923, "raw_hex": "EB900101590400B9040000000097AAAB", "success": true, "data": null, "data_hex": "B90400000000", "data_hex_formatted": ["0x04B9", "0x00000000"]}, {"command": "59", "channel": 1210, "timestamp": 1756091456.696023, "raw_hex": "EB900101590400BA04CDCCCC3E3BAAAB", "success": true, "data": null, "data_hex": "BA04CDCCCC3E", "data_hex_formatted": ["0x04BA", "0x3ECCCCCD"]}, {"command": "59", "channel": 1301, "timestamp": 1756091456.72633, "raw_hex": "EB90010159040015050000803EB2AAAB", "success": true, "data": null, "data_hex": "15050000803E", "data_hex_formatted": ["0x0515", "0x3E800000"]}, {"command": "59", "channel": 1302, "timestamp": 1756091456.7568402, "raw_hex": "EB900101590400160500000000F5AAAB", "success": true, "data": null, "data_hex": "160500000000", "data_hex_formatted": ["0x0516", "0x00000000"]}, {"command": "59", "channel": 1303, "timestamp": 1756091456.7882686, "raw_hex": "EB9001015904001705A69B443BB6AAAB", "success": true, "data": null, "data_hex": "1705A69B443B", "data_hex_formatted": ["0x0517", "0x3B449BA6"]}, {"command": "59", "channel": 1304, "timestamp": 1756091456.8187835, "raw_hex": "EB900101590400180500000000F7AAAB", "success": true, "data": null, "data_hex": "180500000000", "data_hex_formatted": ["0x0518", "0x00000000"]}, {"command": "59", "channel": 1305, "timestamp": 1756091456.8498218, "raw_hex": "EB9001015904001905CDCCCC3E9BAAAB", "success": true, "data": null, "data_hex": "1905CDCCCC3E", "data_hex_formatted": ["0x0519", "0x3ECCCCCD"]}, {"command": "59", "channel": 1306, "timestamp": 1756091456.88088, "raw_hex": "EB9001015904001A05CDCCCCBE1CAAAB", "success": true, "data": null, "data_hex": "1A05CDCCCCBE", "data_hex_formatted": ["0x051A", "0xBECCCCCD"]}, {"command": "59", "channel": 1307, "timestamp": 1756091456.910887, "raw_hex": "EB9001015904001B05CDCCCC3E9DAAAB", "success": true, "data": null, "data_hex": "1B05CDCCCC3E", "data_hex_formatted": ["0x051B", "0x3ECCCCCD"]}, {"command": "59", "channel": 1308, "timestamp": 1756091456.9424324, "raw_hex": "EB9001015904001C05CDCCCCBE1EAAAB", "success": true, "data": null, "data_hex": "1C05CDCCCCBE", "data_hex_formatted": ["0x051C", "0xBECCCCCD"]}, {"command": "59", "channel": 1309, "timestamp": 1756091456.9733372, "raw_hex": "EB9001015904001D05CDCCCC3D9EAAAB", "success": true, "data": null, "data_hex": "1D05CDCCCC3D", "data_hex_formatted": ["0x051D", "0x3DCCCCCD"]}, {"command": "59", "channel": 1310, "timestamp": 1756091457.0040374, "raw_hex": "EB9001015904001E0500000000FDAAAB", "success": true, "data": null, "data_hex": "1E0500000000", "data_hex_formatted": ["0x051E", "0x00000000"]}, {"command": "59", "channel": 1401, "timestamp": 1756091457.0345051, "raw_hex": "EB90010159040079050000003F97AAAB", "success": true, "data": null, "data_hex": "79050000003F", "data_hex_formatted": ["0x0579", "0x3F000000"]}, {"command": "59", "channel": 1402, "timestamp": 1756091457.066264, "raw_hex": "EB9001015904007A050000000059AAAB", "success": true, "data": null, "data_hex": "7A0500000000", "data_hex_formatted": ["0x057A", "0x00000000"]}, {"command": "59", "channel": 1403, "timestamp": 1756091457.0965998, "raw_hex": "EB9001015904007B0517B7D13831AAAB", "success": true, "data": null, "data_hex": "7B0517B7D138", "data_hex_formatted": ["0x057B", "0x38D1B717"]}, {"command": "59", "channel": 1404, "timestamp": 1756091457.1275525, "raw_hex": "EB9001015904007C05000000005BAAAB", "success": true, "data": null, "data_hex": "7C0500000000", "data_hex_formatted": ["0x057C", "0x00000000"]}, {"command": "59", "channel": 1405, "timestamp": 1756091457.1580744, "raw_hex": "EB9001015904007D05CDCC4C3E7FAAAB", "success": true, "data": null, "data_hex": "7D05CDCC4C3E", "data_hex_formatted": ["0x057D", "0x3E4CCCCD"]}, {"command": "59", "channel": 1406, "timestamp": 1756091457.189099, "raw_hex": "EB9001015904007E05CDCC4CBE00AAAB", "success": true, "data": null, "data_hex": "7E05CDCC4CBE", "data_hex_formatted": ["0x057E", "0xBE4CCCCD"]}, {"command": "59", "channel": 1407, "timestamp": 1756091457.2197032, "raw_hex": "EB9001015904007F05CDCC4C3E81AAAB", "success": true, "data": null, "data_hex": "7F05CDCC4C3E", "data_hex_formatted": ["0x057F", "0x3E4CCCCD"]}, {"command": "59", "channel": 1408, "timestamp": 1756091457.2504654, "raw_hex": "EB9001015904008005CDCC4CBE02AAAB", "success": true, "data": null, "data_hex": "8005CDCC4CBE", "data_hex_formatted": ["0x0580", "0xBE4CCCCD"]}, {"command": "59", "channel": 1409, "timestamp": 1756091457.2813458, "raw_hex": "EB90010159040081050000000060AAAB", "success": true, "data": null, "data_hex": "810500000000", "data_hex_formatted": ["0x0581", "0x00000000"]}, {"command": "59", "channel": 1410, "timestamp": 1756091457.3120255, "raw_hex": "EB90010159040082050000803F20AAAB", "success": true, "data": null, "data_hex": "82050000803F", "data_hex_formatted": ["0x0582", "0x3F800000"]}, {"command": "59", "channel": 1501, "timestamp": 1756091457.343353, "raw_hex": "EB900101590400DD05CDCC4CBE5FAAAB", "success": true, "data": null, "data_hex": "DD05CDCC4CBE", "data_hex_formatted": ["0x05DD", "0xBE4CCCCD"]}, {"command": "59", "channel": 1502, "timestamp": 1756091457.3745196, "raw_hex": "EB900101590400DE0500000000BDAAAB", "success": true, "data": null, "data_hex": "DE0500000000", "data_hex_formatted": ["0x05DE", "0x00000000"]}, {"command": "59", "channel": 1503, "timestamp": 1756091457.4051635, "raw_hex": "EB900101590400DF0582A8FB371AAAAB", "success": true, "data": null, "data_hex": "DF0582A8FB37", "data_hex_formatted": ["0x05DF", "0x37FBA882"]}, {"command": "59", "channel": 1504, "timestamp": 1756091457.4367988, "raw_hex": "EB900101590400E00500000000BFAAAB", "success": true, "data": null, "data_hex": "E00500000000", "data_hex_formatted": ["0x05E0", "0x00000000"]}, {"command": "59", "channel": 1505, "timestamp": 1756091457.467209, "raw_hex": "EB900101590400E105CDCCCC3D62AAAB", "success": true, "data": null, "data_hex": "E105CDCCCC3D", "data_hex_formatted": ["0x05E1", "0x3DCCCCCD"]}, {"command": "59", "channel": 1506, "timestamp": 1756091457.498287, "raw_hex": "EB900101590400E205CDCCCCBDE3AAAB", "success": true, "data": null, "data_hex": "E205CDCCCCBD", "data_hex_formatted": ["0x05E2", "0xBDCCCCCD"]}, {"command": "59", "channel": 1507, "timestamp": 1756091457.52902, "raw_hex": "EB900101590400E305CDCC4C3DE4AAAB", "success": true, "data": null, "data_hex": "E305CDCC4C3D", "data_hex_formatted": ["0x05E3", "0x3D4CCCCD"]}, {"command": "59", "channel": 1508, "timestamp": 1756091457.5597541, "raw_hex": "EB900101590400E405CDCC4CBD65AAAB", "success": true, "data": null, "data_hex": "E405CDCC4CBD", "data_hex_formatted": ["0x05E4", "0xBD4CCCCD"]}, {"command": "59", "channel": 1509, "timestamp": 1756091457.5907223, "raw_hex": "EB900101590400E50500000000C4AAAB", "success": true, "data": null, "data_hex": "E50500000000", "data_hex_formatted": ["0x05E5", "0x00000000"]}, {"command": "59", "channel": 1510, "timestamp": 1756091457.620968, "raw_hex": "EB900101590400E60500000000C5AAAB", "success": true, "data": null, "data_hex": "E60500000000", "data_hex_formatted": ["0x05E6", "0x00000000"]}, {"command": "59", "channel": 1601, "timestamp": 1756091457.6520827, "raw_hex": "EB90010159040041060000C8432CAAAB", "success": true, "data": null, "data_hex": "41060000C843", "data_hex_formatted": ["0x0641", "0x43C80000"]}, {"command": "59", "channel": 1602, "timestamp": 1756091457.682855, "raw_hex": "EB9001015904004206DC05000003AAAB", "success": true, "data": null, "data_hex": "4206DC050000", "data_hex_formatted": ["0x0642", "0x000005DC"]}, {"command": "59", "channel": 1603, "timestamp": 1756091457.7147484, "raw_hex": "EB9001015904004306E80300000EAAAB", "success": true, "data": null, "data_hex": "4306E8030000", "data_hex_formatted": ["0x0643", "0x000003E8"]}, {"command": "59", "channel": 1604, "timestamp": 1756091457.7450264, "raw_hex": "EB90010159040044062003000047AAAB", "success": true, "data": null, "data_hex": "440620030000", "data_hex_formatted": ["0x0644", "0x00000320"]}, {"command": "59", "channel": 1605, "timestamp": 1756091457.776753, "raw_hex": "EB9001015904004506F40100001AAAAB", "success": true, "data": null, "data_hex": "4506F4010000", "data_hex_formatted": ["0x0645", "0x000001F4"]}, {"command": "59", "channel": 1606, "timestamp": 1756091457.807173, "raw_hex": "EB90010159040046060000000026AAAB", "success": true, "data": null, "data_hex": "460600000000", "data_hex_formatted": ["0x0646", "0x00000000"]}, {"command": "59", "channel": 1607, "timestamp": 1756091457.8368742, "raw_hex": "EB900101590400470600008C42F5AAAB", "success": true, "data": null, "data_hex": "470600008C42", "data_hex_formatted": ["0x0647", "0x428C0000"]}, {"command": "59", "channel": 1701, "timestamp": 1756091457.868272, "raw_hex": "EB900101590400A5065C8FC24072AAAB", "success": true, "data": null, "data_hex": "A5065C8FC240", "data_hex_formatted": ["0x06A5", "0x40C28F5C"]}, {"command": "59", "channel": 1702, "timestamp": 1756091457.899334, "raw_hex": "EB900101590400A6065C8FC24073AAAB", "success": true, "data": null, "data_hex": "A6065C8FC240", "data_hex_formatted": ["0x06A6", "0x40C28F5C"]}, {"command": "59", "channel": 1703, "timestamp": 1756091457.9297738, "raw_hex": "EB900101590400A7065C8FC24074AAAB", "success": true, "data": null, "data_hex": "A7065C8FC240", "data_hex_formatted": ["0x06A7", "0x40C28F5C"]}, {"command": "59", "channel": 1704, "timestamp": 1756091457.9606214, "raw_hex": "EB900101590400A8065C8FC24075AAAB", "success": true, "data": null, "data_hex": "A8065C8FC240", "data_hex_formatted": ["0x06A8", "0x40C28F5C"]}, {"command": "59", "channel": 1705, "timestamp": 1756091457.992522, "raw_hex": "EB900101590400A9065C8FC24076AAAB", "success": true, "data": null, "data_hex": "A9065C8FC240", "data_hex_formatted": ["0x06A9", "0x40C28F5C"]}, {"command": "59", "channel": 1706, "timestamp": 1756091458.0220103, "raw_hex": "EB900101590400AA065C8FC24077AAAB", "success": true, "data": null, "data_hex": "AA065C8FC240", "data_hex_formatted": ["0x06AA", "0x40C28F5C"]}, {"command": "59", "channel": 1707, "timestamp": 1756091458.0532992, "raw_hex": "EB900101590400AB065C8FC24078AAAB", "success": true, "data": null, "data_hex": "AB065C8FC240", "data_hex_formatted": ["0x06AB", "0x40C28F5C"]}, {"command": "59", "channel": 1708, "timestamp": 1756091458.0844564, "raw_hex": "EB900101590400AC065C8FC24079AAAB", "success": true, "data": null, "data_hex": "AC065C8FC240", "data_hex_formatted": ["0x06AC", "0x40C28F5C"]}, {"command": "59", "channel": 1709, "timestamp": 1756091458.1152878, "raw_hex": "EB900101590400AD065C8FC2407AAAAB", "success": true, "data": null, "data_hex": "AD065C8FC240", "data_hex_formatted": ["0x06AD", "0x40C28F5C"]}, {"command": "59", "channel": 1710, "timestamp": 1756091458.1456256, "raw_hex": "EB900101590400AE065C8FC2407BAAAB", "success": true, "data": null, "data_hex": "AE065C8FC240", "data_hex_formatted": ["0x06AE", "0x40C28F5C"]}, {"command": "59", "channel": 1711, "timestamp": 1756091458.1764836, "raw_hex": "EB900101590400AF065C8FC2407CAAAB", "success": true, "data": null, "data_hex": "AF065C8FC240", "data_hex_formatted": ["0x06AF", "0x40C28F5C"]}, {"command": "59", "channel": 1712, "timestamp": 1756091458.2075365, "raw_hex": "EB900101590400B0065C8FC2407DAAAB", "success": true, "data": null, "data_hex": "B0065C8FC240", "data_hex_formatted": ["0x06B0", "0x40C28F5C"]}, {"command": "59", "channel": 1713, "timestamp": 1756091458.2391033, "raw_hex": "EB900101590400B1065C8FC2407EAAAB", "success": true, "data": null, "data_hex": "B1065C8FC240", "data_hex_formatted": ["0x06B1", "0x40C28F5C"]}, {"command": "59", "channel": 1714, "timestamp": 1756091458.2698, "raw_hex": "EB900101590400B2065C8FC2407FAAAB", "success": true, "data": null, "data_hex": "B2065C8FC240", "data_hex_formatted": ["0x06B2", "0x40C28F5C"]}, {"command": "59", "channel": 1715, "timestamp": 1756091458.3009694, "raw_hex": "EB900101590400B3065C8FC24080AAAB", "success": true, "data": null, "data_hex": "B3065C8FC240", "data_hex_formatted": ["0x06B3", "0x40C28F5C"]}, {"command": "59", "channel": 1716, "timestamp": 1756091458.332323, "raw_hex": "EB900101590400B4065C8FC24081AAAB", "success": true, "data": null, "data_hex": "B4065C8FC240", "data_hex_formatted": ["0x06B4", "0x40C28F5C"]}, {"command": "59", "channel": 1717, "timestamp": 1756091458.3617833, "raw_hex": "EB900101590400B5065C8FC24082AAAB", "success": true, "data": null, "data_hex": "B5065C8FC240", "data_hex_formatted": ["0x06B5", "0x40C28F5C"]}, {"command": "59", "channel": 1718, "timestamp": 1756091458.3936408, "raw_hex": "EB900101590400B6065C8FC24083AAAB", "success": true, "data": null, "data_hex": "B6065C8FC240", "data_hex_formatted": ["0x06B6", "0x40C28F5C"]}, {"command": "59", "channel": 1801, "timestamp": 1756091458.4244595, "raw_hex": "EB900101590400090700000000EAAAAB", "success": true, "data": null, "data_hex": "090700000000", "data_hex_formatted": ["0x0709", "0x00000000"]}, {"command": "59", "channel": 1802, "timestamp": 1756091458.4550388, "raw_hex": "EB9001015904000A0700000000EBAAAB", "success": true, "data": null, "data_hex": "0A0700000000", "data_hex_formatted": ["0x070A", "0x00000000"]}, {"command": "59", "channel": 1803, "timestamp": 1756091458.4858665, "raw_hex": "EB9001015904000B0700000000ECAAAB", "success": true, "data": null, "data_hex": "0B0700000000", "data_hex_formatted": ["0x070B", "0x00000000"]}, {"command": "59", "channel": 1805, "timestamp": 1756091458.5477495, "raw_hex": "EB9001015904000D0700000000EEAAAB", "success": true, "data": null, "data_hex": "0D0700000000", "data_hex_formatted": ["0x070D", "0x00000000"]}, {"command": "59", "channel": 1806, "timestamp": 1756091458.5790322, "raw_hex": "EB9001015904000E0700000000EFAAAB", "success": true, "data": null, "data_hex": "0E0700000000", "data_hex_formatted": ["0x070E", "0x00000000"]}, {"command": "59", "channel": 1807, "timestamp": 1756091458.609092, "raw_hex": "EB9001015904000F0700000000F0AAAB", "success": true, "data": null, "data_hex": "0F0700000000", "data_hex_formatted": ["0x070F", "0x00000000"]}, {"command": "59", "channel": 1808, "timestamp": 1756091458.6399226, "raw_hex": "EB900101590400100700000000F1AAAB", "success": true, "data": null, "data_hex": "100700000000", "data_hex_formatted": ["0x0710", "0x00000000"]}, {"command": "59", "channel": 1809, "timestamp": 1756091458.6710796, "raw_hex": "EB900101590400110700000000F2AAAB", "success": true, "data": null, "data_hex": "110700000000", "data_hex_formatted": ["0x0711", "0x00000000"]}, {"command": "59", "channel": 1810, "timestamp": 1756091458.7007768, "raw_hex": "EB900101590400120700000000F3AAAB", "success": true, "data": null, "data_hex": "120700000000", "data_hex_formatted": ["0x0712", "0x00000000"]}, {"command": "59", "channel": 1811, "timestamp": 1756091458.732748, "raw_hex": "EB900101590400130700000000F4AAAB", "success": true, "data": null, "data_hex": "130700000000", "data_hex_formatted": ["0x0713", "0x00000000"]}, {"command": "59", "channel": 1812, "timestamp": 1756091458.763282, "raw_hex": "EB900101590400140700000000F5AAAB", "success": true, "data": null, "data_hex": "140700000000", "data_hex_formatted": ["0x0714", "0x00000000"]}, {"command": "59", "channel": 1813, "timestamp": 1756091458.793983, "raw_hex": "EB900101590400150700000000F6AAAB", "success": true, "data": null, "data_hex": "150700000000", "data_hex_formatted": ["0x0715", "0x00000000"]}, {"command": "59", "channel": 1814, "timestamp": 1756091458.825396, "raw_hex": "EB900101590400160700000000F7AAAB", "success": true, "data": null, "data_hex": "160700000000", "data_hex_formatted": ["0x0716", "0x00000000"]}, {"command": "59", "channel": 1815, "timestamp": 1756091458.8560917, "raw_hex": "EB900101590400170700000000F8AAAB", "success": true, "data": null, "data_hex": "170700000000", "data_hex_formatted": ["0x0717", "0x00000000"]}, {"command": "59", "channel": 1816, "timestamp": 1756091458.887409, "raw_hex": "EB900101590400180700F07F45ADAAAB", "success": true, "data": null, "data_hex": "180700F07F45", "data_hex_formatted": ["0x0718", "0x457FF000"]}, {"command": "59", "channel": 1817, "timestamp": 1756091458.9181828, "raw_hex": "EB900101590400190700F07F45AEAAAB", "success": true, "data": null, "data_hex": "190700F07F45", "data_hex_formatted": ["0x0719", "0x457FF000"]}, {"command": "59", "channel": 1818, "timestamp": 1756091458.9498465, "raw_hex": "EB9001015904001A0700F07F45AFAAAB", "success": true, "data": null, "data_hex": "1A0700F07F45", "data_hex_formatted": ["0x071A", "0x457FF000"]}, {"command": "59", "channel": 1901, "timestamp": 1756091458.9802809, "raw_hex": "EB9001015904006D070000F0417FAAAB", "success": true, "data": null, "data_hex": "6D070000F041", "data_hex_formatted": ["0x076D", "0x41F00000"]}, {"command": "59", "channel": 1902, "timestamp": 1756091459.0111346, "raw_hex": "EB9001015904006E0700401C46F1AAAB", "success": true, "data": null, "data_hex": "6E0700401C46", "data_hex_formatted": ["0x076E", "0x461C4000"]}, {"command": "59", "channel": 1903, "timestamp": 1756091459.042183, "raw_hex": "EB9001015904006F0700002842BAAAAB", "success": true, "data": null, "data_hex": "6F0700002842", "data_hex_formatted": ["0x076F", "0x42280000"]}, {"command": "59", "channel": 1904, "timestamp": 1756091459.0724933, "raw_hex": "EB900101590400700700803B4450AAAB", "success": true, "data": null, "data_hex": "700700803B44", "data_hex_formatted": ["0x0770", "0x443B8000"]}, {"command": "59", "channel": 1905, "timestamp": 1756091459.1034899, "raw_hex": "EB90010159040071070000000052AAAB", "success": true, "data": null, "data_hex": "710700000000", "data_hex_formatted": ["0x0771", "0x00000000"]}, {"command": "59", "channel": 1906, "timestamp": 1756091459.1344242, "raw_hex": "EB90010159040072070000FA4390AAAB", "success": true, "data": null, "data_hex": "72070000FA43", "data_hex_formatted": ["0x0772", "0x43FA0000"]}, {"command": "59", "channel": 1907, "timestamp": 1756091459.1656566, "raw_hex": "EB90010159040073076666264086AAAB", "success": true, "data": null, "data_hex": "730766662640", "data_hex_formatted": ["0x0773", "0x40266666"]}, {"command": "59", "channel": 1908, "timestamp": 1756091459.1967225, "raw_hex": "EB9001015904007407A4707D3F25AAAB", "success": true, "data": null, "data_hex": "7407A4707D3F", "data_hex_formatted": ["0x0774", "0x3F7D70A4"]}, {"command": "59", "channel": 1909, "timestamp": 1756091459.2270303, "raw_hex": "EB9001015904007507B893883A63AAAB", "success": true, "data": null, "data_hex": "7507B893883A", "data_hex_formatted": ["0x0775", "0x3A8893B8"]}, {"command": "59", "channel": 1910, "timestamp": 1756091459.2585154, "raw_hex": "EB9001015904007607CDCCCC3EFAAAAB", "success": true, "data": null, "data_hex": "7607CDCCCC3E", "data_hex_formatted": ["0x0776", "0x3ECCCCCD"]}, {"command": "59", "channel": 1911, "timestamp": 1756091459.2891095, "raw_hex": "EB90010159040077070AD7233C98AAAB", "success": true, "data": null, "data_hex": "77070AD7233C", "data_hex_formatted": ["0x0777", "0x3C23D70A"]}, {"command": "59", "channel": 1912, "timestamp": 1756091459.3197484, "raw_hex": "EB90010159040078070000003F98AAAB", "success": true, "data": null, "data_hex": "78070000003F", "data_hex_formatted": ["0x0778", "0x3F000000"]}, {"command": "59", "channel": 1913, "timestamp": 1756091459.3511436, "raw_hex": "EB9001015904007907B893883A67AAAB", "success": true, "data": null, "data_hex": "7907B893883A", "data_hex_formatted": ["0x0779", "0x3A8893B8"]}, {"command": "59", "channel": 1914, "timestamp": 1756091459.381873, "raw_hex": "EB9001015904007A07A88ED02081AAAB", "success": true, "data": null, "data_hex": "7A07A88ED020", "data_hex_formatted": ["0x077A", "0x20D08EA8"]}, {"command": "59", "channel": 1915, "timestamp": 1756091459.413287, "raw_hex": "EB9001015904007B070000964234AAAB", "success": true, "data": null, "data_hex": "7B0700009642", "data_hex_formatted": ["0x077B", "0x42960000"]}, {"command": "59", "channel": 1916, "timestamp": 1756091459.4441452, "raw_hex": "EB9001015904007C0700004843E8AAAB", "success": true, "data": null, "data_hex": "7C0700004843", "data_hex_formatted": ["0x077C", "0x43480000"]}, {"command": "59", "channel": 1917, "timestamp": 1756091459.4749117, "raw_hex": "EB9001015904007D070000803F1DAAAB", "success": true, "data": null, "data_hex": "7D070000803F", "data_hex_formatted": ["0x077D", "0x3F800000"]}, {"command": "59", "channel": 1918, "timestamp": 1756091459.5051746, "raw_hex": "EB9001015904007E070000803F1EAAAB", "success": true, "data": null, "data_hex": "7E070000803F", "data_hex_formatted": ["0x077E", "0x3F800000"]}, {"command": "59", "channel": 1919, "timestamp": 1756091459.5365083, "raw_hex": "EB9001015904007F070000C8426AAAAB", "success": true, "data": null, "data_hex": "7F070000C842", "data_hex_formatted": ["0x077F", "0x42C80000"]}, {"command": "59", "channel": 1920, "timestamp": 1756091459.5675259, "raw_hex": "EB90010159040080070000000061AAAB", "success": true, "data": null, "data_hex": "800700000000", "data_hex_formatted": ["0x0780", "0x00000000"]}, {"command": "59", "channel": 1921, "timestamp": 1756091459.5985942, "raw_hex": "EB9001015904008107CDCC4C3D84AAAB", "success": true, "data": null, "data_hex": "8107CDCC4C3D", "data_hex_formatted": ["0x0781", "0x3D4CCCCD"]}, {"command": "59", "channel": 1922, "timestamp": 1756091459.629083, "raw_hex": "EB900101590400820700004842EDAAAB", "success": true, "data": null, "data_hex": "820700004842", "data_hex_formatted": ["0x0782", "0x42480000"]}, {"command": "59", "channel": 1923, "timestamp": 1756091459.6598947, "raw_hex": "EB90010159040083070000000064AAAB", "success": true, "data": null, "data_hex": "830700000000", "data_hex_formatted": ["0x0783", "0x00000000"]}, {"command": "59", "channel": 1924, "timestamp": 1756091459.6908596, "raw_hex": "EB90010159040084070000000065AAAB", "success": true, "data": null, "data_hex": "840700000000", "data_hex_formatted": ["0x0784", "0x00000000"]}, {"command": "59", "channel": 1925, "timestamp": 1756091459.7215264, "raw_hex": "EB90010159040085070000803F25AAAB", "success": true, "data": null, "data_hex": "85070000803F", "data_hex_formatted": ["0x0785", "0x3F800000"]}, {"command": "59", "channel": 1926, "timestamp": 1756091459.752852, "raw_hex": "EB90010159040086070000000067AAAB", "success": true, "data": null, "data_hex": "860700000000", "data_hex_formatted": ["0x0786", "0x00000000"]}, {"command": "59", "channel": 1927, "timestamp": 1756091459.7837956, "raw_hex": "EB90010159040087070000803F27AAAB", "success": true, "data": null, "data_hex": "87070000803F", "data_hex_formatted": ["0x0787", "0x3F800000"]}, {"command": "59", "channel": 1928, "timestamp": 1756091459.8147702, "raw_hex": "EB9001015904008807CDCC4C3F8DAAAB", "success": true, "data": null, "data_hex": "8807CDCC4C3F", "data_hex_formatted": ["0x0788", "0x3F4CCCCD"]}, {"command": "59", "channel": 1929, "timestamp": 1756091459.844287, "raw_hex": "EB90010159040089079A99193FF5AAAB", "success": true, "data": null, "data_hex": "89079A99193F", "data_hex_formatted": ["0x0789", "0x3F19999A"]}, {"command": "59", "channel": 1930, "timestamp": 1756091459.8753843, "raw_hex": "EB9001015904008A070000C84275AAAB", "success": true, "data": null, "data_hex": "8A070000C842", "data_hex_formatted": ["0x078A", "0x42C80000"]}, {"command": "59", "channel": 1931, "timestamp": 1756091459.905883, "raw_hex": "EB9001015904008B076666E63F5DAAAB", "success": true, "data": null, "data_hex": "8B076666E63F", "data_hex_formatted": ["0x078B", "0x3FE66666"]}, {"command": "59", "channel": 1932, "timestamp": 1756091459.9367094, "raw_hex": "EB9001015904008C073333733F85AAAB", "success": true, "data": null, "data_hex": "8C073333733F", "data_hex_formatted": ["0x078C", "0x3F733333"]}, {"command": "59", "channel": 1933, "timestamp": 1756091459.967819, "raw_hex": "EB9001015904008D070000803F2DAAAB", "success": true, "data": null, "data_hex": "8D070000803F", "data_hex_formatted": ["0x078D", "0x3F800000"]}, {"command": "59", "channel": 1934, "timestamp": 1756091459.99823, "raw_hex": "EB9001015904008E070000803F2EAAAB", "success": true, "data": null, "data_hex": "8E070000803F", "data_hex_formatted": ["0x078E", "0x3F800000"]}, {"command": "59", "channel": 1935, "timestamp": 1756091460.0296814, "raw_hex": "EB9001015904008F0700401C4612AAAB", "success": true, "data": null, "data_hex": "8F0700401C46", "data_hex_formatted": ["0x078F", "0x461C4000"]}, {"command": "59", "channel": 1936, "timestamp": 1756091460.0603025, "raw_hex": "EB9001015904009007B81E853F0BAAAB", "success": true, "data": null, "data_hex": "9007B81E853F", "data_hex_formatted": ["0x0790", "0x3F851EB8"]}, {"command": "59", "channel": 1937, "timestamp": 1756091460.0915942, "raw_hex": "EB90010159040091070AD7A33C32AAAB", "success": true, "data": null, "data_hex": "91070AD7A33C", "data_hex_formatted": ["0x0791", "0x3CA3D70A"]}, {"command": "59", "channel": 1938, "timestamp": 1756091460.1220186, "raw_hex": "EB90010159040092070000803F32AAAB", "success": true, "data": null, "data_hex": "92070000803F", "data_hex_formatted": ["0x0792", "0x3F800000"]}, {"command": "59", "channel": 1939, "timestamp": 1756091460.1526506, "raw_hex": "EB90010159040093076F12833AB2AAAB", "success": true, "data": null, "data_hex": "93076F12833A", "data_hex_formatted": ["0x0793", "0x3A83126F"]}, {"command": "59", "channel": 1940, "timestamp": 1756091460.1839125, "raw_hex": "EB90010159040094070000FA44B3AAAB", "success": true, "data": null, "data_hex": "94070000FA44", "data_hex_formatted": ["0x0794", "0x44FA0000"]}, {"command": "59", "channel": 1941, "timestamp": 1756091460.214283, "raw_hex": "EB90010159040095070000000076AAAB", "success": true, "data": null, "data_hex": "950700000000", "data_hex_formatted": ["0x0795", "0x00000000"]}, {"command": "59", "channel": 1942, "timestamp": 1756091460.2449193, "raw_hex": "EB90010159040096070000000077AAAB", "success": true, "data": null, "data_hex": "960700000000", "data_hex_formatted": ["0x0796", "0x00000000"]}, {"command": "59", "channel": 1943, "timestamp": 1756091460.276017, "raw_hex": "EB90010159040097070000000078AAAB", "success": true, "data": null, "data_hex": "970700000000", "data_hex_formatted": ["0x0797", "0x00000000"]}, {"command": "59", "channel": 1944, "timestamp": 1756091460.3067956, "raw_hex": "EB90010159040098070000000079AAAB", "success": true, "data": null, "data_hex": "980700000000", "data_hex_formatted": ["0x0798", "0x00000000"]}, {"command": "59", "channel": 1945, "timestamp": 1756091460.3374476, "raw_hex": "EB9001015904009907000000007AAAAB", "success": true, "data": null, "data_hex": "990700000000", "data_hex_formatted": ["0x0799", "0x00000000"]}, {"command": "59", "channel": 1946, "timestamp": 1756091460.368339, "raw_hex": "EB9001015904009A07000000007BAAAB", "success": true, "data": null, "data_hex": "9A0700000000", "data_hex_formatted": ["0x079A", "0x00000000"]}, {"command": "59", "channel": 1947, "timestamp": 1756091460.3990912, "raw_hex": "EB9001015904009B07000000007CAAAB", "success": true, "data": null, "data_hex": "9B0700000000", "data_hex_formatted": ["0x079B", "0x00000000"]}, {"command": "59", "channel": 1948, "timestamp": 1756091460.4299128, "raw_hex": "EB9001015904009C07000000007DAAAB", "success": true, "data": null, "data_hex": "9C0700000000", "data_hex_formatted": ["0x079C", "0x00000000"]}, {"command": "59", "channel": 1949, "timestamp": 1756091460.4604812, "raw_hex": "EB9001015904009D07000000007EAAAB", "success": true, "data": null, "data_hex": "9D0700000000", "data_hex_formatted": ["0x079D", "0x00000000"]}, {"command": "59", "channel": 1950, "timestamp": 1756091460.4910126, "raw_hex": "EB9001015904009E07CDCCCC3D21AAAB", "success": true, "data": null, "data_hex": "9E07CDCCCC3D", "data_hex_formatted": ["0x079E", "0x3DCCCCCD"]}, {"command": "59", "channel": 1951, "timestamp": 1756091460.5215669, "raw_hex": "EB9001015904009F0717B7D13857AAAB", "success": true, "data": null, "data_hex": "9F0717B7D138", "data_hex_formatted": ["0x079F", "0x38D1B717"]}, {"command": "59", "channel": 1952, "timestamp": 1756091460.5527241, "raw_hex": "EB900101590400A0070000000081AAAB", "success": true, "data": null, "data_hex": "A00700000000", "data_hex_formatted": ["0x07A0", "0x00000000"]}, {"command": "59", "channel": 1953, "timestamp": 1756091460.5835083, "raw_hex": "EB900101590400A107CDCCCC3F26AAAB", "success": true, "data": null, "data_hex": "A107CDCCCC3F", "data_hex_formatted": ["0x07A1", "0x3FCCCCCD"]}, {"command": "59", "channel": 1954, "timestamp": 1756091460.6146555, "raw_hex": "EB900101590400A2070000000083AAAB", "success": true, "data": null, "data_hex": "A20700000000", "data_hex_formatted": ["0x07A2", "0x00000000"]}, {"command": "59", "channel": 1955, "timestamp": 1756091460.6450481, "raw_hex": "EB900101590400A30700000041C5AAAB", "success": true, "data": null, "data_hex": "A30700000041", "data_hex_formatted": ["0x07A3", "0x41000000"]}, {"command": "59", "channel": 1956, "timestamp": 1756091460.6760845, "raw_hex": "EB900101590400A4076F12833AC3AAAB", "success": true, "data": null, "data_hex": "A4076F12833A", "data_hex_formatted": ["0x07A4", "0x3A83126F"]}, {"command": "59", "channel": 1957, "timestamp": 1756091460.707156, "raw_hex": "EB900101590400A507CDCC4C3DA8AAAB", "success": true, "data": null, "data_hex": "A507CDCC4C3D", "data_hex_formatted": ["0x07A5", "0x3D4CCCCD"]}, {"command": "59", "channel": 1958, "timestamp": 1756091460.7372434, "raw_hex": "EB900101590400A6076F12033B46AAAB", "success": true, "data": null, "data_hex": "A6076F12033B", "data_hex_formatted": ["0x07A6", "0x3B03126F"]}, {"command": "59", "channel": 1959, "timestamp": 1756091460.7689798, "raw_hex": "EB900101590400A7076F12033B47AAAB", "success": true, "data": null, "data_hex": "A7076F12033B", "data_hex_formatted": ["0x07A7", "0x3B03126F"]}, {"command": "59", "channel": 1960, "timestamp": 1756091460.7997043, "raw_hex": "EB900101590400A80770000000F9AAAB", "success": true, "data": null, "data_hex": "A80770000000", "data_hex_formatted": ["0x07A8", "0x00000070"]}, {"command": "59", "channel": 1961, "timestamp": 1756091460.8305755, "raw_hex": "EB900101590400A907000000008AAAAB", "success": true, "data": null, "data_hex": "A90700000000", "data_hex_formatted": ["0x07A9", "0x00000000"]}, {"command": "59", "channel": 1962, "timestamp": 1756091460.8614843, "raw_hex": "EB900101590400AA07000000008BAAAB", "success": true, "data": null, "data_hex": "AA0700000000", "data_hex_formatted": ["0x07AA", "0x00000000"]}, {"command": "59", "channel": 1963, "timestamp": 1756091460.892273, "raw_hex": "EB900101590400AB07000000008CAAAB", "success": true, "data": null, "data_hex": "AB0700000000", "data_hex_formatted": ["0x07AB", "0x00000000"]}, {"command": "59", "channel": 1964, "timestamp": 1756091460.9227147, "raw_hex": "EB900101590400AC07000000008DAAAB", "success": true, "data": null, "data_hex": "AC0700000000", "data_hex_formatted": ["0x07AC", "0x00000000"]}, {"command": "59", "channel": 1965, "timestamp": 1756091460.953654, "raw_hex": "EB900101590400AD07000000008EAAAB", "success": true, "data": null, "data_hex": "AD0700000000", "data_hex_formatted": ["0x07AD", "0x00000000"]}, {"command": "59", "channel": 2001, "timestamp": 1756091460.984653, "raw_hex": "EB900101590400D1073333933FEAAAAB", "success": true, "data": null, "data_hex": "D1073333933F", "data_hex_formatted": ["0x07D1", "0x3F933333"]}, {"command": "59", "channel": 2002, "timestamp": 1756091461.0154805, "raw_hex": "EB900101590400D20720BF020094AAAB", "success": true, "data": null, "data_hex": "D20720BF0200", "data_hex_formatted": ["0x07D2", "0x0002BF20"]}, {"command": "59", "channel": 2003, "timestamp": 1756091461.0465431, "raw_hex": "EB900101590400D3073333B33F0CAAAB", "success": true, "data": null, "data_hex": "D3073333B33F", "data_hex_formatted": ["0x07D3", "0x3FB33333"]}, {"command": "59", "channel": 2004, "timestamp": 1756091461.0775511, "raw_hex": "EB900101590400D407E8030000A0AAAB", "success": true, "data": null, "data_hex": "D407E8030000", "data_hex_formatted": ["0x07D4", "0x000003E8"]}, {"command": "59", "channel": 2005, "timestamp": 1756091461.1075914, "raw_hex": "EB900101590400D5070000204016AAAB", "success": true, "data": null, "data_hex": "D50700002040", "data_hex_formatted": ["0x07D5", "0x40200000"]}, {"command": "59", "channel": 2006, "timestamp": 1756091461.1386864, "raw_hex": "EB900101590400D607640000001BAAAB", "success": true, "data": null, "data_hex": "D60764000000", "data_hex_formatted": ["0x07D6", "0x00000064"]}, {"command": "59", "channel": 2007, "timestamp": 1756091461.170094, "raw_hex": "EB900101590400D7070000404038AAAB", "success": true, "data": null, "data_hex": "D70700004040", "data_hex_formatted": ["0x07D7", "0x40400000"]}, {"command": "59", "channel": 2008, "timestamp": 1756091461.2007527, "raw_hex": "EB900101590400D8073333333F91AAAB", "success": true, "data": null, "data_hex": "D8073333333F", "data_hex_formatted": ["0x07D8", "0x3F333333"]}, {"command": "59", "channel": 2009, "timestamp": 1756091461.2317371, "raw_hex": "EB900101590400D907B80B00007DAAAB", "success": true, "data": null, "data_hex": "D907B80B0000", "data_hex_formatted": ["0x07D9", "0x00000BB8"]}, {"command": "59", "channel": 2010, "timestamp": 1756091461.2619107, "raw_hex": "EB900101590400DA070000003FFAAAAB", "success": true, "data": null, "data_hex": "DA070000003F", "data_hex_formatted": ["0x07DA", "0x3F000000"]}, {"command": "59", "channel": 2011, "timestamp": 1756091461.2924953, "raw_hex": "EB900101590400DB07E8030000A7AAAB", "success": true, "data": null, "data_hex": "DB07E8030000", "data_hex_formatted": ["0x07DB", "0x000003E8"]}, {"command": "59", "channel": 2012, "timestamp": 1756091461.323481, "raw_hex": "EB900101590400DC07295C8F3E0FAAAB", "success": true, "data": null, "data_hex": "DC07295C8F3E", "data_hex_formatted": ["0x07DC", "0x3E8F5C29"]}, {"command": "59", "channel": 2013, "timestamp": 1756091461.3546638, "raw_hex": "EB900101590400DD078A0200004AAAAB", "success": true, "data": null, "data_hex": "DD078A020000", "data_hex_formatted": ["0x07DD", "0x0000028A"]}, {"command": "59", "channel": 2014, "timestamp": 1756091461.3855486, "raw_hex": "EB900101590400DE07CDCCCC3D61AAAB", "success": true, "data": null, "data_hex": "DE07CDCCCC3D", "data_hex_formatted": ["0x07DE", "0x3DCCCCCD"]}, {"command": "59", "channel": 2015, "timestamp": 1756091461.4178362, "raw_hex": "EB900101590400DF07B80B000083AAAB", "success": true, "data": null, "data_hex": "DF07B80B0000", "data_hex_formatted": ["0x07DF", "0x00000BB8"]}, {"command": "59", "channel": 2016, "timestamp": 1756091461.44847, "raw_hex": "EB900101590400E0070AD7233C01AAAB", "success": true, "data": null, "data_hex": "E0070AD7233C", "data_hex_formatted": ["0x07E0", "0x3C23D70A"]}, {"command": "59", "channel": 2017, "timestamp": 1756091461.4796002, "raw_hex": "EB900101590400E1076400000026AAAB", "success": true, "data": null, "data_hex": "E10764000000", "data_hex_formatted": ["0x07E1", "0x00000064"]}, {"command": "59", "channel": 2018, "timestamp": 1756091461.5092528, "raw_hex": "EB900101590400E2073075000068AAAB", "success": true, "data": null, "data_hex": "E20730750000", "data_hex_formatted": ["0x07E2", "0x00007530"]}, {"command": "59", "channel": 2019, "timestamp": 1756091461.5404375, "raw_hex": "EB900101590400E307CDCC8C3F28AAAB", "success": true, "data": null, "data_hex": "E307CDCC8C3F", "data_hex_formatted": ["0x07E3", "0x3F8CCCCD"]}, {"command": "59", "channel": 2020, "timestamp": 1756091461.5710864, "raw_hex": "EB900101590400E40720BF0200A6AAAB", "success": true, "data": null, "data_hex": "E40720BF0200", "data_hex_formatted": ["0x07E4", "0x0002BF20"]}, {"command": "59", "channel": 2021, "timestamp": 1756091461.6018002, "raw_hex": "EB900101590400E5079A99993FD1AAAB", "success": true, "data": null, "data_hex": "E5079A99993F", "data_hex_formatted": ["0x07E5", "0x3F99999A"]}, {"command": "59", "channel": 2022, "timestamp": 1756091461.6329124, "raw_hex": "EB900101590400E607640000002BAAAB", "success": true, "data": null, "data_hex": "E60764000000", "data_hex_formatted": ["0x07E6", "0x00000064"]}, {"command": "59", "channel": 2023, "timestamp": 1756091461.6637304, "raw_hex": "EB900101590400E70766660640DAAAAB", "success": true, "data": null, "data_hex": "E70766660640", "data_hex_formatted": ["0x07E7", "0x40066666"]}, {"command": "59", "channel": 2024, "timestamp": 1756091461.6943414, "raw_hex": "EB900101590400E80700000000C9AAAB", "success": true, "data": null, "data_hex": "E80700000000", "data_hex_formatted": ["0x07E8", "0x00000000"]}, {"command": "59", "channel": 2025, "timestamp": 1756091461.7250724, "raw_hex": "EB900101590400E9073C00000006AAAB", "success": true, "data": null, "data_hex": "E9073C000000", "data_hex_formatted": ["0x07E9", "0x0000003C"]}, {"command": "59", "channel": 2026, "timestamp": 1756091461.7565587, "raw_hex": "EB900101590400EA077E0400004DAAAB", "success": true, "data": null, "data_hex": "EA077E040000", "data_hex_formatted": ["0x07EA", "0x0000047E"]}, {"command": "59", "channel": 2027, "timestamp": 1756091461.7873857, "raw_hex": "EB900101590400EB07F4010000C1AAAB", "success": true, "data": null, "data_hex": "EB07F4010000", "data_hex_formatted": ["0x07EB", "0x000001F4"]}, {"command": "59", "channel": 2028, "timestamp": 1756091461.817605, "raw_hex": "EB900101590400EC0766662640FFAAAB", "success": true, "data": null, "data_hex": "EC0766662640", "data_hex_formatted": ["0x07EC", "0x40266666"]}, {"command": "59", "channel": 2029, "timestamp": 1756091461.8489912, "raw_hex": "EB900101590400ED0702000000D0AAAB", "success": true, "data": null, "data_hex": "ED0702000000", "data_hex_formatted": ["0x07ED", "0x00000002"]}, {"command": "59", "channel": 2030, "timestamp": 1756091461.880107, "raw_hex": "EB900101590400EE070A000000D9AAAB", "success": true, "data": null, "data_hex": "EE070A000000", "data_hex_formatted": ["0x07EE", "0x0000000A"]}, {"command": "59", "channel": 2031, "timestamp": 1756091461.9112065, "raw_hex": "EB900101590400EF0720BF0200B1AAAB", "success": true, "data": null, "data_hex": "EF0720BF0200", "data_hex_formatted": ["0x07EF", "0x0002BF20"]}, {"command": "59", "channel": 2032, "timestamp": 1756091461.9418821, "raw_hex": "EB900101590400F007CDCC4C3DF3AAAB", "success": true, "data": null, "data_hex": "F007CDCC4C3D", "data_hex_formatted": ["0x07F0", "0x3D4CCCCD"]}, {"command": "59", "channel": 2033, "timestamp": 1756091461.972608, "raw_hex": "EB900101590400F1070000404052AAAB", "success": true, "data": null, "data_hex": "F10700004040", "data_hex_formatted": ["0x07F1", "0x40400000"]}, {"command": "59", "channel": 2034, "timestamp": 1756091462.003292, "raw_hex": "EB900101590400F20703000000D6AAAB", "success": true, "data": null, "data_hex": "F20703000000", "data_hex_formatted": ["0x07F2", "0x00000003"]}, {"command": "59", "channel": 2035, "timestamp": 1756091462.0344515, "raw_hex": "EB900101590400F3070000004014AAAB", "success": true, "data": null, "data_hex": "F30700000040", "data_hex_formatted": ["0x07F3", "0x40000000"]}, {"command": "59", "channel": 2036, "timestamp": 1756091462.0652654, "raw_hex": "EB900101590400F40703000000D8AAAB", "success": true, "data": null, "data_hex": "F40703000000", "data_hex_formatted": ["0x07F4", "0x00000003"]}, {"command": "59", "channel": 2101, "timestamp": 1756091462.096519, "raw_hex": "EB9001015904003508FFE7FD9F99AAAB", "success": true, "data": null, "data_hex": "3508FFE7FD9F", "data_hex_formatted": ["0x0835", "0x9FFDE7FF"]}, {"command": "59", "channel": 2102, "timestamp": 1756091462.1267784, "raw_hex": "EB9001015904003608E30910182CAAAB", "success": true, "data": null, "data_hex": "3608E3091018", "data_hex_formatted": ["0x0836", "0x181009E3"]}, {"command": "59", "channel": 2201, "timestamp": 1756091462.1565232, "raw_hex": "EB90010159040099080102030485AAAB", "success": true, "data": null, "data_hex": "990801020304", "data_hex_formatted": ["0x0899", "0x04030201"]}, {"command": "59", "channel": 2202, "timestamp": 1756091462.187526, "raw_hex": "EB9001015904009A080C00000088AAAB", "success": true, "data": null, "data_hex": "9A080C000000", "data_hex_formatted": ["0x089A", "0x0000000C"]}, {"command": "59", "channel": 2203, "timestamp": 1756091462.218518, "raw_hex": "EB9001015904009B080506070998AAAB", "success": true, "data": null, "data_hex": "9B0805060709", "data_hex_formatted": ["0x089B", "0x09070605"]}, {"command": "59", "channel": 2301, "timestamp": 1756091462.2495131, "raw_hex": "EB900101590400FD088585078777AAAB", "success": true, "data": null, "data_hex": "FD0885850787", "data_hex_formatted": ["0x08FD", "0x87078585"]}, {"command": "59", "channel": 2302, "timestamp": 1756091462.2805717, "raw_hex": "EB900101590400FE080B8B098908AAAB", "success": true, "data": null, "data_hex": "FE080B8B0989", "data_hex_formatted": ["0x08FE", "0x89098B0B"]}, {"command": "59", "channel": 2303, "timestamp": 1756091462.3109975, "raw_hex": "EB900101590400FF0810000000F1AAAB", "success": true, "data": null, "data_hex": "FF0810000000", "data_hex_formatted": ["0x08FF", "0x00000010"]}, {"command": "59", "channel": 2304, "timestamp": 1756091462.3421726, "raw_hex": "EB9001015904000009CDCC4C3E06AAAB", "success": true, "data": null, "data_hex": "0009CDCC4C3E", "data_hex_formatted": ["0x0900", "0x3E4CCCCD"]}, {"command": "59", "channel": 2305, "timestamp": 1756091462.3729706, "raw_hex": "EB9001015904000109CDCC4C3E07AAAB", "success": true, "data": null, "data_hex": "0109CDCC4C3E", "data_hex_formatted": ["0x0901", "0x3E4CCCCD"]}, {"command": "59", "channel": 2306, "timestamp": 1756091462.4042346, "raw_hex": "EB9001015904000209CDCC4C3E08AAAB", "success": true, "data": null, "data_hex": "0209CDCC4C3E", "data_hex_formatted": ["0x0902", "0x3E4CCCCD"]}, {"command": "59", "channel": 2307, "timestamp": 1756091462.4349623, "raw_hex": "EB9001015904000309CDCC4C3E09AAAB", "success": true, "data": null, "data_hex": "0309CDCC4C3E", "data_hex_formatted": ["0x0903", "0x3E4CCCCD"]}, {"command": "59", "channel": 2308, "timestamp": 1756091462.4657245, "raw_hex": "EB9001015904000409CDCC4C3E0AAAAB", "success": true, "data": null, "data_hex": "0409CDCC4C3E", "data_hex_formatted": ["0x0904", "0x3E4CCCCD"]}, {"command": "59", "channel": 2309, "timestamp": 1756091462.4968603, "raw_hex": "EB9001015904000509CDCC4C3E0BAAAB", "success": true, "data": null, "data_hex": "0509CDCC4C3E", "data_hex_formatted": ["0x0905", "0x3E4CCCCD"]}, {"command": "59", "channel": 2310, "timestamp": 1756091462.527438, "raw_hex": "EB9001015904000609CDCC4C3E0CAAAB", "success": true, "data": null, "data_hex": "0609CDCC4C3E", "data_hex_formatted": ["0x0906", "0x3E4CCCCD"]}, {"command": "59", "channel": 2311, "timestamp": 1756091462.5586753, "raw_hex": "EB9001015904000709CDCC4C3E0DAAAB", "success": true, "data": null, "data_hex": "0709CDCC4C3E", "data_hex_formatted": ["0x0907", "0x3E4CCCCD"]}, {"command": "59", "channel": 2312, "timestamp": 1756091462.5890713, "raw_hex": "EB9001015904000809CDCC4C3E0EAAAB", "success": true, "data": null, "data_hex": "0809CDCC4C3E", "data_hex_formatted": ["0x0908", "0x3E4CCCCD"]}, {"command": "59", "channel": 2313, "timestamp": 1756091462.6204412, "raw_hex": "EB900101590400090900000000ECAAAB", "success": true, "data": null, "data_hex": "090900000000", "data_hex_formatted": ["0x0909", "0x00000000"]}, {"command": "59", "channel": 2314, "timestamp": 1756091462.6514757, "raw_hex": "EB9001015904000A0900000000EDAAAB", "success": true, "data": null, "data_hex": "0A0900000000", "data_hex_formatted": ["0x090A", "0x00000000"]}, {"command": "59", "channel": 2315, "timestamp": 1756091462.683406, "raw_hex": "EB9001015904000B0900000000EEAAAB", "success": true, "data": null, "data_hex": "0B0900000000", "data_hex_formatted": ["0x090B", "0x00000000"]}, {"command": "59", "channel": 2316, "timestamp": 1756091462.7132852, "raw_hex": "EB9001015904000C09CDCC4C3E12AAAB", "success": true, "data": null, "data_hex": "0C09CDCC4C3E", "data_hex_formatted": ["0x090C", "0x3E4CCCCD"]}, {"command": "59", "channel": 2317, "timestamp": 1756091462.7447767, "raw_hex": "EB9001015904000D0900000000F0AAAB", "success": true, "data": null, "data_hex": "0D0900000000", "data_hex_formatted": ["0x090D", "0x00000000"]}, {"command": "59", "channel": 2318, "timestamp": 1756091462.7746089, "raw_hex": "EB9001015904000E0900000000F1AAAB", "success": true, "data": null, "data_hex": "0E0900000000", "data_hex_formatted": ["0x090E", "0x00000000"]}, {"command": "59", "channel": 2319, "timestamp": 1756091462.8058345, "raw_hex": "EB9001015904000F0900000000F2AAAB", "success": true, "data": null, "data_hex": "0F0900000000", "data_hex_formatted": ["0x090F", "0x00000000"]}, {"command": "59", "channel": 2320, "timestamp": 1756091462.8367076, "raw_hex": "EB90010159040010090000803FB2AAAB", "success": true, "data": null, "data_hex": "10090000803F", "data_hex_formatted": ["0x0910", "0x3F800000"]}, {"command": "59", "channel": 2321, "timestamp": 1756091462.8687472, "raw_hex": "EB90010159040011090000803FB3AAAB", "success": true, "data": null, "data_hex": "11090000803F", "data_hex_formatted": ["0x0911", "0x3F800000"]}, {"command": "59", "channel": 2322, "timestamp": 1756091462.8997097, "raw_hex": "EB90010159040012090000803FB4AAAB", "success": true, "data": null, "data_hex": "12090000803F", "data_hex_formatted": ["0x0912", "0x3F800000"]}, {"command": "59", "channel": 2323, "timestamp": 1756091462.9296849, "raw_hex": "EB90010159040013090000803FB5AAAB", "success": true, "data": null, "data_hex": "13090000803F", "data_hex_formatted": ["0x0913", "0x3F800000"]}, {"command": "59", "channel": 2324, "timestamp": 1756091462.9608603, "raw_hex": "EB90010159040014090000803FB6AAAB", "success": true, "data": null, "data_hex": "14090000803F", "data_hex_formatted": ["0x0914", "0x3F800000"]}, {"command": "59", "channel": 2325, "timestamp": 1756091462.9918838, "raw_hex": "EB90010159040015090000803FB7AAAB", "success": true, "data": null, "data_hex": "15090000803F", "data_hex_formatted": ["0x0915", "0x3F800000"]}, {"command": "59", "channel": 2326, "timestamp": 1756091463.0232518, "raw_hex": "EB90010159040016090000803FB8AAAB", "success": true, "data": null, "data_hex": "16090000803F", "data_hex_formatted": ["0x0916", "0x3F800000"]}, {"command": "59", "channel": 2327, "timestamp": 1756091463.0533063, "raw_hex": "EB90010159040017090000803FB9AAAB", "success": true, "data": null, "data_hex": "17090000803F", "data_hex_formatted": ["0x0917", "0x3F800000"]}, {"command": "59", "channel": 2328, "timestamp": 1756091463.0838976, "raw_hex": "EB900101590400180917B7D138D2AAAB", "success": true, "data": null, "data_hex": "180917B7D138", "data_hex_formatted": ["0x0918", "0x38D1B717"]}, {"command": "59", "channel": 2329, "timestamp": 1756091463.114957, "raw_hex": "EB900101590400190917B7D138D3AAAB", "success": true, "data": null, "data_hex": "190917B7D138", "data_hex_formatted": ["0x0919", "0x38D1B717"]}, {"command": "59", "channel": 2330, "timestamp": 1756091463.1451073, "raw_hex": "EB9001015904001A0917B7D138D4AAAB", "success": true, "data": null, "data_hex": "1A0917B7D138", "data_hex_formatted": ["0x091A", "0x38D1B717"]}, {"command": "59", "channel": 2331, "timestamp": 1756091463.176495, "raw_hex": "EB9001015904001B0917B7D138D5AAAB", "success": true, "data": null, "data_hex": "1B0917B7D138", "data_hex_formatted": ["0x091B", "0x38D1B717"]}, {"command": "59", "channel": 2332, "timestamp": 1756091463.206358, "raw_hex": "EB9001015904001C0917B7D138D6AAAB", "success": true, "data": null, "data_hex": "1C0917B7D138", "data_hex_formatted": ["0x091C", "0x38D1B717"]}, {"command": "59", "channel": 2333, "timestamp": 1756091463.237552, "raw_hex": "EB9001015904001D0917B7D138D7AAAB", "success": true, "data": null, "data_hex": "1D0917B7D138", "data_hex_formatted": ["0x091D", "0x38D1B717"]}, {"command": "59", "channel": 2334, "timestamp": 1756091463.2683487, "raw_hex": "EB9001015904001E0917B7D138D8AAAB", "success": true, "data": null, "data_hex": "1E0917B7D138", "data_hex_formatted": ["0x091E", "0x38D1B717"]}, {"command": "59", "channel": 2335, "timestamp": 1756091463.2999187, "raw_hex": "EB9001015904001F0917B7D138D9AAAB", "success": true, "data": null, "data_hex": "1F0917B7D138", "data_hex_formatted": ["0x091F", "0x38D1B717"]}, {"command": "59", "channel": 2401, "timestamp": 1756091463.330745, "raw_hex": "EB9001015904006109000040C044AAAB", "success": true, "data": null, "data_hex": "6109000040C0", "data_hex_formatted": ["0x0961", "0xC0400000"]}, {"command": "59", "channel": 2402, "timestamp": 1756091463.3618364, "raw_hex": "EB90010159040062090000000045AAAB", "success": true, "data": null, "data_hex": "620900000000", "data_hex_formatted": ["0x0962", "0x00000000"]}, {"command": "59", "channel": 2403, "timestamp": 1756091463.3935933, "raw_hex": "EB90010159040063090AD7233D87AAAB", "success": true, "data": null, "data_hex": "63090AD7233D", "data_hex_formatted": ["0x0963", "0x3D23D70A"]}, {"command": "59", "channel": 2404, "timestamp": 1756091463.4232638, "raw_hex": "EB90010159040064090000000047AAAB", "success": true, "data": null, "data_hex": "640900000000", "data_hex_formatted": ["0x0964", "0x00000000"]}, {"command": "59", "channel": 2405, "timestamp": 1756091463.4541333, "raw_hex": "EB90010159040065090000803F07AAAB", "success": true, "data": null, "data_hex": "65090000803F", "data_hex_formatted": ["0x0965", "0x3F800000"]}, {"command": "59", "channel": 2406, "timestamp": 1756091463.4857495, "raw_hex": "EB9001015904006609000080BF88AAAB", "success": true, "data": null, "data_hex": "6609000080BF", "data_hex_formatted": ["0x0966", "0xBF800000"]}, {"command": "59", "channel": 2407, "timestamp": 1756091463.5165913, "raw_hex": "EB90010159040067090000803F09AAAB", "success": true, "data": null, "data_hex": "67090000803F", "data_hex_formatted": ["0x0967", "0x3F800000"]}, {"command": "59", "channel": 2408, "timestamp": 1756091463.5472014, "raw_hex": "EB9001015904006809000080BF8AAAAB", "success": true, "data": null, "data_hex": "6809000080BF", "data_hex_formatted": ["0x0968", "0xBF800000"]}, {"command": "59", "channel": 2409, "timestamp": 1756091463.5778904, "raw_hex": "EB9001015904006909000000004CAAAB", "success": true, "data": null, "data_hex": "690900000000", "data_hex_formatted": ["0x0969", "0x00000000"]}, {"command": "59", "channel": 2410, "timestamp": 1756091463.6080954, "raw_hex": "EB9001015904006A090000803F0CAAAB", "success": true, "data": null, "data_hex": "6A090000803F", "data_hex_formatted": ["0x096A", "0x3F800000"]}, {"command": "59", "channel": 2501, "timestamp": 1756091463.6399562, "raw_hex": "EB900101590400C509000040C0A8AAAB", "success": true, "data": null, "data_hex": "C509000040C0", "data_hex_formatted": ["0x09C5", "0xC0400000"]}, {"command": "59", "channel": 2502, "timestamp": 1756091463.6704605, "raw_hex": "EB900101590400C60900000000A9AAAB", "success": true, "data": null, "data_hex": "C60900000000", "data_hex_formatted": ["0x09C6", "0x00000000"]}, {"command": "59", "channel": 2503, "timestamp": 1756091463.701499, "raw_hex": "EB900101590400C7090AD7233DEBAAAB", "success": true, "data": null, "data_hex": "C7090AD7233D", "data_hex_formatted": ["0x09C7", "0x3D23D70A"]}, {"command": "59", "channel": 2504, "timestamp": 1756091463.731965, "raw_hex": "EB900101590400C80900000000ABAAAB", "success": true, "data": null, "data_hex": "C80900000000", "data_hex_formatted": ["0x09C8", "0x00000000"]}, {"command": "59", "channel": 2505, "timestamp": 1756091463.7628253, "raw_hex": "EB900101590400C9090000803F6BAAAB", "success": true, "data": null, "data_hex": "C9090000803F", "data_hex_formatted": ["0x09C9", "0x3F800000"]}, {"command": "59", "channel": 2506, "timestamp": 1756091463.7948098, "raw_hex": "EB900101590400CA09000080BFECAAAB", "success": true, "data": null, "data_hex": "CA09000080BF", "data_hex_formatted": ["0x09CA", "0xBF800000"]}, {"command": "59", "channel": 2507, "timestamp": 1756091463.8253675, "raw_hex": "EB900101590400CB090000803F6DAAAB", "success": true, "data": null, "data_hex": "CB090000803F", "data_hex_formatted": ["0x09CB", "0x3F800000"]}, {"command": "59", "channel": 2508, "timestamp": 1756091463.8568811, "raw_hex": "EB900101590400CC09000080BFEEAAAB", "success": true, "data": null, "data_hex": "CC09000080BF", "data_hex_formatted": ["0x09CC", "0xBF800000"]}, {"command": "59", "channel": 2509, "timestamp": 1756091463.8871746, "raw_hex": "EB900101590400CD0900000000B0AAAB", "success": true, "data": null, "data_hex": "CD0900000000", "data_hex_formatted": ["0x09CD", "0x00000000"]}, {"command": "59", "channel": 2510, "timestamp": 1756091463.917674, "raw_hex": "EB900101590400CE0900000000B1AAAB", "success": true, "data": null, "data_hex": "CE0900000000", "data_hex_formatted": ["0x09CE", "0x00000000"]}, {"command": "59", "channel": 1804, "timestamp": 1756091458.516237, "raw_hex": "EB9001015904000C0700000000EDAAAB", "success": true, "data": null, "data_hex": "0C0700000000", "data_hex_formatted": ["0x070C", "0x00000000"]}, {"command": "C5", "timestamp": 1756091464.234257, "raw_hex": "EB900101C530000000060325200603252088888888000000008888888800000000888888880000000088888888888888880000000001000001B0AAAB", "success": true, "data": {"主控CPU板DSP版本号": {"raw_bytes": "06 03 25 20", "version": "2.0", "date": "25-03-06", "group_index": 1}, "主控CPU板CPLD版本号": {"raw_bytes": "06 03 25 20", "version": "2.0", "date": "25-03-06", "group_index": 2}, "主控A相PWM板DSP版本号": {"raw_bytes": "88 88 88 88", "version": "8.8", "date": "88-88-88", "group_index": 3}, "主控A相PWM板FPGA版本号": {"raw_bytes": "00 00 00 00", "version": "0.0", "date": "00-00-00", "group_index": 4}, "主控B相PWM板DSP版本号": {"raw_bytes": "88 88 88 88", "version": "8.8", "date": "88-88-88", "group_index": 5}, "主控B相PWM板FPGA版本号": {"raw_bytes": "00 00 00 00", "version": "0.0", "date": "00-00-00", "group_index": 6}, "主控C相PWM板DSP版本号": {"raw_bytes": "88 88 88 88", "version": "8.8", "date": "88-88-88", "group_index": 7}, "主控C相PWM板FPGA版本号": {"raw_bytes": "00 00 00 00", "version": "0.0", "date": "00-00-00", "group_index": 8}, "主控COMM板DSP版本号": {"raw_bytes": "88 88 88 88", "version": "8.8", "date": "88-88-88", "group_index": 9}, "主控COMM板CPLD版本号": {"raw_bytes": "88 88 88 88", "version": "8.8", "date": "88-88-88", "group_index": 10}, "主控CPU板FPGA版本号": {"raw_bytes": "00 00 00 00", "version": "0.0", "date": "00-00-00", "group_index": 11}, "主控CPU板子版本号": {"raw_bytes": "01 00 00 01", "version": "0.1", "date": "00-00-01", "group_index": 12}}, "data_hex": "060325200603252088888888000000008888888800000000888888880000000088888888888888880000000001000001", "data_hex_formatted": []}, {"command": "D6", "timestamp": 1756091464.5054102, "raw_hex": "EB900101D62400000000000000000000000000000000000000000000000000000000000000000000000000000077AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D7", "timestamp": 1756091464.7694693, "raw_hex": "EB900101D72400000000000000000000000000000000000000000000000000000000000000000000000000000078AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D8", "timestamp": 1756091465.0450773, "raw_hex": "EB900101D82400000000000000000000000000000000000000000000000000000000000000000000000000000079AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "C1", "timestamp": 1756095955.0757353, "raw_hex": "EB900101C1440000000002000000000000000000000000000000000000000008000800080000000004000000000000000000000000000000000000000000000000000000000000000000000000A0AAAB", "success": true, "data": null, "data_hex": "0002000000000000000000000000000000000000000008000800080000000004000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0200", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0000", "0x0400", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "C4", "timestamp": 1756095956.7183044, "raw_hex": "EB900101C4C00000001347C43AB815463BD550CB3AD2344F3BB6E4BABA5CF6203CA590943CEE7E0E3C47EE7F3B452F173B0C841E3A9C25D1BB60BDE73B365D1F3BCA58F53AA1DA70BA0000000000000000000000000000000000000000CDCCCC3DE09C293F0000000000000000000000006CF99937CD8186393B0220BB24D26ABC000000000000000070A4CC3AB81E853F577A2138CE7116385FDF163876D419381D6ABB3A94AD413B27C7C93A000000005CF6203CA590943CEE7E0E3CA8F60E3C0AE0813C2AC7F73BB7AAAB", "success": true, "data": null, "data_hex": "1347C43AB815463BD550CB3AD2344F3BB6E4BABA5CF6203CA590943CEE7E0E3C47EE7F3B452F173B0C841E3A9C25D1BB60BDE73B365D1F3BCA58F53AA1DA70BA0000000000000000000000000000000000000000CDCCCC3DE09C293F0000000000000000000000006CF99937CD8186393B0220BB24D26ABC000000000000000070A4CC3AB81E853F577A2138CE7116385FDF163876D419381D6ABB3A94AD413B27C7C93A000000005CF6203CA590943CEE7E0E3CA8F60E3C0AE0813C2AC7F73B", "data_hex_formatted": ["0x3AC44713", "0x3B4615B8", "0x3ACB50D5", "0x3B4F34D2", "0xBABAE4B6", "0x3C20F65C", "0x3C9490A5", "0x3C0E7EEE", "0x3B7FEE47", "0x3B172F45", "0x3A1E840C", "0xBBD1259C", "0x3BE7BD60", "0x3B1F5D36", "0x3AF558CA", "0xBA70DAA1", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3DCCCCCD", "0x3F299CE0", "0x00000000", "0x00000000", "0x00000000", "0x3799F96C", "0x398681CD", "0xBB20023B", "0xBC6AD224", "0x00000000", "0x00000000", "0x3ACCA470", "0x3F851EB8", "0x38217A57", "0x381671CE", "0x3816DF5F", "0x3819D476", "0x3ABB6A1D", "0x3B41AD94", "0x3AC9C727", "0x00000000", "0x3C20F65C", "0x3C9490A5", "0x3C0E7EEE", "0x3C0EF6A8", "0x3C81E00A", "0x3BF7C72A"]}, {"command": "D0", "timestamp": 1756095955.7621741, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D1", "timestamp": 1756095956.026118, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D2", "timestamp": 1756095956.3182657, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "C4", "timestamp": 0, "raw_hex": "", "success": false, "data": null, "data_hex": "", "data_hex_formatted": []}, {"command": "D3", "timestamp": 1756095956.9843647, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D4", "timestamp": 1756095953.823774, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}, {"command": "D5", "timestamp": 1756095954.1038463, "raw_hex": "EB900101D5240000000000000000000000000000000000000000000000000000000000000000000000030003007CAAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000003000300", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0003", "0x0003"]}]