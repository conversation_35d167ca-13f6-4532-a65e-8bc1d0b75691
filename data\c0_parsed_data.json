[{"id": "HMI_30018_0", "name": "启动按钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_1", "name": "停止按钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_2", "name": "复位按钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_3", "name": "备用按钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_4", "name": "急停按钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_5", "name": "SVG断路器输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_6", "name": "启动柜接触器输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_7", "name": "启动柜隔离刀闸输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_8", "name": "启动柜接地刀闸输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_9", "name": "远方旋钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_10", "name": "就地旋钮输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_11", "name": "水冷系统电源故障输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_12", "name": "水冷系统预警输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_13", "name": "水冷系统请求跳闸输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_14", "name": "水冷系统请求停止输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30018_15", "name": "水冷系统运行/停止输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_0", "name": "母联1状态输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_1", "name": "母联2状态输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_2", "name": "风机运行状态输入", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_3", "name": "备用", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_4", "name": "输入21", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_5", "name": "输入22", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_6", "name": "输入23", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_7", "name": "输入24", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_8", "name": "输入25", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_9", "name": "输入26", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_10", "name": "输入27", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_11", "name": "输入28", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_12", "name": "输入29", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_13", "name": "输入30", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_14", "name": "输入31", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30019_15", "name": "输入32", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_0", "name": "SVG断路器允许合闸输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_1", "name": "SVG断路器合闸命令输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_2", "name": "SVG断路器分闸命令输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_3", "name": "启动柜接触器合闸命令输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_4", "name": "启动柜接触器分闸命令输出", "ts": "2025-08-21 18:58:23.068", "value": "1"}, {"id": "HMI_30020_5", "name": "运行指示灯输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_6", "name": "故障指示灯输出", "ts": "2025-08-21 18:58:23.068", "value": "1"}, {"id": "HMI_30020_7", "name": "就绪指示灯输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_8", "name": "报警指示灯输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_9", "name": "保留指示灯输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_10", "name": "SVG运行去水冷输出", "ts": "2025-08-21 18:58:23.068", "value": "1"}, {"id": "HMI_30020_11", "name": "水冷启动输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_12", "name": "水冷停止输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_13", "name": "远程故障输出", "ts": "2025-08-21 18:58:23.068", "value": "1"}, {"id": "HMI_30020_14", "name": "风机合闸输出", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30020_15", "name": "故障报警脉冲输出", "ts": "2025-08-21 18:58:23.068", "value": "1"}, {"id": "HMI_30021_0", "name": "输出17", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_1", "name": "输出18", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_2", "name": "输出19", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_3", "name": "输出20", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_4", "name": "输出21", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_5", "name": "输出22", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_6", "name": "输出23", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_7", "name": "输出24", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_8", "name": "输出25", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_9", "name": "输出26", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_10", "name": "输出27", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_11", "name": "输出28", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_12", "name": "输出29", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_13", "name": "输出30", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_14", "name": "输出31", "ts": "2025-08-21 18:58:23.068", "value": "0"}, {"id": "HMI_30021_15", "name": "输出32", "ts": "2025-08-21 18:58:23.068", "value": "0"}]