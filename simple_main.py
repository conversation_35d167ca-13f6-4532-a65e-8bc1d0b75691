#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化串口通信服务
严格按照实际运行报文和协议文档实现
"""

import asyncio
import json
import serial
import time
import struct
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class SimpleDataRotator:
    """简单的数据文件轮转器"""

    def __init__(self, data_dir: str = "data", max_files: int = 3):
        """
        初始化数据轮转器

        Args:
            data_dir: 数据目录
            max_files: 最大文件数量
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.max_files = max_files
        self.current_file_index = 0
        self.startup_file_created = False

    def get_current_file_path(self) -> Path:
        """
        获取当前数据文件路径

        Returns:
            当前文件路径
        """
        return self.data_dir / f"data_{self.current_file_index}.json"

    def rotate_file(self):
        """轮转到下一个文件"""
        self.current_file_index = (self.current_file_index + 1) % self.max_files

        # 清空新的当前文件
        current_file = self.get_current_file_path()
        if current_file.exists():
            current_file.unlink()

    def save_startup_data(self, data: Dict[str, Any]):
        """
        保存启动序列数据到专门的文件

        Args:
            data: 要保存的启动序列数据
        """
        startup_file = self.data_dir / "startup_sequence.json"

        # 如果是第一次保存启动数据，创建新文件
        if not self.startup_file_created:
            with open(startup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')
            self.startup_file_created = True
        else:
            # 追加数据到启动文件
            with open(startup_file, 'a', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                f.write('\n')

    def save_data(self, data: Dict[str, Any]):
        """
        保存数据到当前文件

        Args:
            data: 要保存的数据
        """
        current_file = self.get_current_file_path()

        # 检查文件大小，如果超过500KB则轮转
        if current_file.exists() and current_file.stat().st_size > 500 * 1024:
            self.rotate_file()
            current_file = self.get_current_file_path()

        # 追加数据到文件
        with open(current_file, 'a', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False)
            f.write('\n')

    def save_all_data(self, data: Dict[str, Any]):
        """
        保存所有命令数据到固定的实时数据文件，只保留特定命令序列
        对于59命令，保存259帧数据，每帧包含通道序号

        Args:
            data: 要保存的数据
        """
        realtime_file = self.data_dir / "realtime_data.json"

        # 定义固定的命令序列
        fixed_commands = ['C0', '59', 'C5', 'D6', 'D7', 'D8', 'C1', 'C4', 'D0', 'D1', 'D2', 'C4', 'D3', 'D4', 'D5']

        # 只处理在固定命令序列中的命令
        if data.get('command') not in fixed_commands:
            return

        # 初始化文件（如果不存在）
        if not realtime_file.exists():
            # 创建初始的固定结构
            initial_data = []
            for cmd in fixed_commands:
                if cmd == '59':
                    # 为59命令创建259个条目，每个条目对应一个通道
                    for channel in range(259):
                        initial_data.append({
                            "command": cmd,
                            "channel": channel,
                            "timestamp": 0,
                            "raw_hex": "",
                            "success": False,
                            "data": None,
                            "data_hex": "",
                            "data_hex_formatted": []
                        })
                else:
                    initial_data.append({
                        "command": cmd,
                        "timestamp": 0,
                        "raw_hex": "",
                        "success": False,
                        "data": None,
                        "data_hex": "",
                        "data_hex_formatted": []
                    })

            with open(realtime_file, 'w', encoding='utf-8') as f:
                json.dump(initial_data, f, ensure_ascii=False, indent=2)

        # 读取现有数据
        try:
            with open(realtime_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            # 如果文件损坏或不存在，重新创建
            existing_data = []
            for cmd in fixed_commands:
                if cmd == '59':
                    # 为59命令创建259个条目，每个条目对应一个通道
                    for channel in range(259):
                        existing_data.append({
                            "command": cmd,
                            "channel": channel,
                            "timestamp": 0,
                            "raw_hex": "",
                            "success": False,
                            "data": None,
                            "data_hex": "",
                            "data_hex_formatted": []
                        })
                else:
                    existing_data.append({
                        "command": cmd,
                        "timestamp": 0,
                        "raw_hex": "",
                        "success": False,
                        "data": None,
                        "data_hex": "",
                        "data_hex_formatted": []
                    })

        # 更新数据
        command_to_update = data.get('command')

        if command_to_update == '59':
            # 对于59命令，需要从data_hex_formatted中提取通道序号
            channel_number = None
            if data.get('data_hex_formatted') and len(data['data_hex_formatted']) > 0:
                # 从第一个hex值中提取通道序号
                try:
                    channel_hex = data['data_hex_formatted'][0]
                    if channel_hex.startswith('0x'):
                        channel_number = int(channel_hex, 16)
                except (ValueError, IndexError):
                    print(f"无法解析59命令的通道序号: {data.get('data_hex_formatted')}")
                    return

            if channel_number is not None:
                # 查找是否已存在该通道的条目
                found_item = None
                for i, item in enumerate(existing_data):
                    if (item.get('command') == '59' and
                        item.get('channel') == channel_number):
                        found_item = item
                        break

                if found_item:
                    # 更新现有条目
                    found_item['timestamp'] = data.get('timestamp', found_item['timestamp'])
                    found_item['raw_hex'] = data.get('raw_hex', found_item['raw_hex'])
                    found_item['success'] = data.get('success', found_item['success'])
                    found_item['data'] = data.get('data', found_item['data'])
                    found_item['data_hex'] = data.get('data_hex', found_item['data_hex'])
                    found_item['data_hex_formatted'] = data.get('data_hex_formatted', found_item['data_hex_formatted'])
                else:
                    # 如果不存在该通道，创建新条目并插入到59命令区域
                    new_item = {
                        "command": "59",
                        "channel": channel_number,
                        "timestamp": data.get('timestamp', 0),
                        "raw_hex": data.get('raw_hex', ""),
                        "success": data.get('success', False),
                        "data": data.get('data'),
                        "data_hex": data.get('data_hex', ""),
                        "data_hex_formatted": data.get('data_hex_formatted', [])
                    }

                    # 找到59命令区域的结束位置，插入新条目
                    insert_index = len(existing_data)
                    for i, item in enumerate(existing_data):
                        if item.get('command') == 'C5':  # C5是59命令后的第一个命令
                            insert_index = i
                            break

                    existing_data.insert(insert_index, new_item)
                    print(f"为通道{channel_number}创建新的59命令条目")
        else:
            # 对于其他命令，找到第一个匹配项并更新
            for i, item in enumerate(existing_data):
                if item.get('command') == command_to_update and 'channel' not in item:
                    # 只更新指定的字段
                    item['timestamp'] = data.get('timestamp', item['timestamp'])
                    item['raw_hex'] = data.get('raw_hex', item['raw_hex'])
                    item['success'] = data.get('success', item['success'])
                    item['data'] = data.get('data', item['data'])
                    item['data_hex'] = data.get('data_hex', item['data_hex'])
                    item['data_hex_formatted'] = data.get('data_hex_formatted', item['data_hex_formatted'])
                    break

        # 写回文件
        with open(realtime_file, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)


class RealProtocolParser:
    """真实协议解析器，严格按照实际报文格式解析"""

    def __init__(self):
        """初始化协议解析器"""
        # 根据实际报文定义命令格式
        self.commands = {
            'C0': {'name': 'IO状态查询', 'response_data_length': 8},
            'C1': {'name': '故障报警查询', 'response_data_length': 68},
            'C3': {'name': '故障录波查询', 'response_data_length': 200},
            'C4': {'name': 'DSP状态查询', 'response_data_length': 192},
            'C5': {'name': '版本信息查询', 'response_data_length': 48},
            '59': {'name': '调试参数查询', 'response_data_length': 4, 'multi_frame': True},
            '57': {'name': '调试参数修改', 'response_data_length': 0},
            '55': {'name': '修改返回确认', 'response_data_length': 8},
            '37': {'name': '修改完成确认', 'response_data_length': 0},
            '5B': {'name': '修改完成确认返回', 'response_data_length': 8},
            'D0': {'name': 'A相单元直流侧电压', 'response_data_length': 36},
            'D1': {'name': 'B相单元直流侧电压', 'response_data_length': 36},
            'D2': {'name': 'C相单元直流侧电压', 'response_data_length': 36},
            'D3': {'name': 'A相单元状态', 'response_data_length': 36},
            'D4': {'name': 'B相单元状态', 'response_data_length': 36},
            'D5': {'name': 'C相单元状态', 'response_data_length': 36},
            'D6': {'name': 'A相单元程序版本信息', 'response_data_length': 36},
            'D7': {'name': 'B相单元程序版本信息', 'response_data_length': 36},
            'D8': {'name': 'C相单元程序版本信息', 'response_data_length': 36}
        }

    def calculate_checksum(self, data: bytes) -> int:
        """
        计算单字节和校验

        Args:
            data: 待校验的数据

        Returns:
            校验值
        """
        return sum(data) % 256

    def build_command(self, command: str, data_bytes: bytes = b'', channel_info: bytes = b'') -> bytes:
        """
        构建命令帧

        Args:
            command: 命令代码
            data_bytes: 数据字节
            channel_info: 通道信息（用于C3命令）

        Returns:
            完整的命令帧
        """
        # 帧头
        frame = bytearray([0xEB, 0x90, 0x01, 0x01])

        # 命令
        frame.append(int(command, 16))

        # 根据命令类型构建不同格式
        if command == 'C3':  # 故障录波特殊格式
            frame.append(0xC8)  # 数据字节数
            frame.append(0x00)  # 空字节
            frame.extend(channel_info if channel_info else b'\x00\x00')  # 通道信息
            frame.extend(b'\x00' * 5)  # 填充到13字节
        elif command == '57':  # 调试参数修改特殊格式
            frame.append(0x04)  # 数据字节数
            frame.append(0x00)  # 空字节
            frame.extend(data_bytes[:6] if len(data_bytes) >= 6 else data_bytes + b'\x00' * (6 - len(data_bytes)))
        else:  # 其他命令标准格式
            frame.extend(b'\x00' * 8)  # 8个空字节

        # 计算校验
        checksum = self.calculate_checksum(frame)
        frame.append(checksum)

        # 帧尾
        frame.extend([0xAA, 0xAB])

        return bytes(frame)

    def parse_c5_version_info(self, data_bytes: bytes) -> Dict[str, Any]:
        """
        解析C5版本信息数据，严格按照协议文档第4.3节的解析规则

        Args:
            data_bytes: 48字节的版本信息数据

        Returns:
            解析后的版本信息字典
        """
        if len(data_bytes) != 48:
            raise ValueError(f"C5版本信息数据长度必须为48字节，实际收到{len(data_bytes)}字节")

        # 模块名称映射表，按照协议文档定义的12个模块
        module_names = {
            1: "主控CPU板DSP版本号",
            2: "主控CPU板CPLD版本号",
            3: "主控A相PWM板DSP版本号",
            4: "主控A相PWM板FPGA版本号",
            5: "主控B相PWM板DSP版本号",
            6: "主控B相PWM板FPGA版本号",
            7: "主控C相PWM板DSP版本号",
            8: "主控C相PWM板FPGA版本号",
            9: "主控COMM板DSP版本号",
            10: "主控COMM板CPLD版本号",
            11: "主控CPU板FPGA版本号",
            12: "主控CPU板子版本号"
        }

        version_info = {}

        # 逐组解析，每组4字节，共12组
        for i in range(12):
            start_idx = i * 4
            group_data = data_bytes[start_idx:start_idx + 4]

            module_name = module_names[i + 1]

            # 按照协议文档解析规则：
            # - 第4个字节（索引3）：版本号
            # - 前3个字节（索引0-2）：修改日期
            version_byte = group_data[3]  # 第4个字节是版本号
            date_bytes = group_data[:3]   # 前3个字节是日期

            # 版本号解析：十六进制转十进制显示
            if version_byte == 0x88:
                # 特殊值0x88表示无效数据，显示为8.8
                version_str = "8.8"
            else:
                # 正常版本号：高4位为主版本，低4位为次版本
                major = version_byte >> 4  # 高4位
                minor = version_byte & 0x0F  # 低4位
                version_str = f"{major}.{minor}"

            # 日期解析：YY-MM-DD格式
            if all(b == 0x88 for b in date_bytes):
                # 全部为0x88表示无效日期
                date_str = "88-88-88"
            elif all(b == 0x00 for b in date_bytes):
                # 全部为0x00表示未设置日期
                date_str = "00-00-00"
            else:
                # 正常日期：按字节位置解析
                # 根据文档示例：23 09 15 83 -> 日期15-09-23
                # 注意：这里的字节值应该直接作为BCD码来解释
                # 即：0x23表示23，0x09表示09，0x15表示15
                yy = date_bytes[2]  # 年份（BCD格式）
                mm = date_bytes[1]  # 月份（BCD格式）
                dd = date_bytes[0]  # 日期（BCD格式）

                # 将BCD码转换为十进制显示
                yy_decimal = (yy >> 4) * 10 + (yy & 0x0F)
                mm_decimal = (mm >> 4) * 10 + (mm & 0x0F)
                dd_decimal = (dd >> 4) * 10 + (dd & 0x0F)

                date_str = f"{yy_decimal:02d}-{mm_decimal:02d}-{dd_decimal:02d}"

            # 保存解析结果
            version_info[module_name] = {
                'raw_bytes': ' '.join(f"{b:02X}" for b in group_data),
                'version': version_str,
                'date': date_str,
                'group_index': i + 1  # 组号（1-12）
            }

        return version_info

    def parse_response(self, command: str, raw_data: bytes) -> Dict[str, Any]:
        """
        解析命令响应数据

        Args:
            command: 命令代码
            raw_data: 原始响应数据

        Returns:
            解析结果
        """
        result = {
            'command': command,
            'timestamp': time.time(),
            'raw_hex': raw_data.hex().upper(),
            'success': False,
            'data': None
        }

        if len(raw_data) < 9:  # 最小帧长度检查（包含校验和）
            result['error'] = f'响应帧太短: {len(raw_data)} 字节，至少需要9字节进行校验和验证'
            return result

        # 检查帧头和帧尾，增加容错处理
        if raw_data[:4] != b'\xEB\x90\x01\x01':
            # 尝试在数据中查找有效的帧头
            header_index = raw_data.find(b'\xEB\x90\x01\x01')
            if header_index > 0:
                print(f"找到偏移的帧头，从位置 {header_index} 开始重新解析")
                raw_data = raw_data[header_index:]
                if len(raw_data) < 9:  # 最小帧长度检查（包含校验和）
                    result['error'] = f'截取后响应帧太短: {len(raw_data)} 字节，至少需要9字节进行校验和验证'
                    return result
            else:
                result['error'] = '帧头不正确'
                return result

        if raw_data[-2:] != b'\xAA\xAB':
            # 尝试在数据中查找有效的帧尾
            tail_index = raw_data.rfind(b'\xAA\xAB')
            if tail_index > 4 and tail_index + 2 <= len(raw_data):  # 确保帧尾在帧头之后
                print(f"找到偏移的帧尾，截取到位置 {tail_index+2}")
                raw_data = raw_data[:tail_index+2]
                if len(raw_data) < 9:  # 最小帧长度检查（包含校验和）
                    result['error'] = f'截取后响应帧太短: {len(raw_data)} 字节，至少需要9字节进行校验和验证'
                    return result
            else:
                result['error'] = '帧尾不正确'
                return result

        # 校验和验证
        # 提取响应帧中的校验和字节（倒数第3个字节）
        received_checksum = raw_data[-3]
        # 计算除校验和外的所有字节的校验和
        data_for_checksum = raw_data[:-3]  # 除去校验和和帧尾的数据
        calculated_checksum = self.calculate_checksum(data_for_checksum)
        # 比较计算值与接收值是否一致
        if received_checksum != calculated_checksum:
            result['error'] = f'校验和不匹配，期望: {calculated_checksum:02X}, 实际: {received_checksum:02X}'
            return result

        # 检查命令码
        response_cmd = f'{raw_data[4]:02X}'
        if response_cmd != command:
            # 特殊处理：59命令可能是设备主动上报的数据，不应该视为错误
            if response_cmd == '59':
                # 处理59命令的响应
                result['command'] = response_cmd  # 更新为实际收到的命令
                if len(raw_data) >= 13:
                    data_length = raw_data[5]
                    if data_length == 0x04:
                        # 提取序号和数据
                        seq_bytes = raw_data[7:9]
                        data_bytes = raw_data[9:13]

                        sequence = struct.unpack('<H', seq_bytes)[0]  # 小端序号
                        float_value = struct.unpack('<f', data_bytes)[0]  # 小端浮点数

                        result['data_hex'] = raw_data[7:13].hex().upper()
                        # 59命令：序号（2字节）+ 浮点数据（4字节）
                        result['data_hex_formatted'] = [f'0x{sequence:04X}', f'0x{int.from_bytes(data_bytes, byteorder="little"):08X}']

                        result['success'] = True
                        result['note'] = f'收到设备主动上报的59命令数据，原期望命令: {command}'
                        return result
            # 其他命令不匹配情况
            result['error'] = f'响应命令不匹配，期望: {command}, 实际: {response_cmd}'
            return result

        # 提取数据部分
        if command == '59':  # 59命令特殊处理
            if len(raw_data) >= 13:
                data_length = raw_data[5]
                if data_length == 0x04:
                    # 提取序号和数据
                    seq_bytes = raw_data[7:9]
                    data_bytes = raw_data[9:13]

                    sequence = struct.unpack('<H', seq_bytes)[0]  # 小端序号
                    float_value = struct.unpack('<f', data_bytes)[0]  # 小端浮点数

                    result['data_hex'] = raw_data[7:13].hex().upper()
                    # 59命令：序号（2字节）+ 浮点数据（4字节）
                    result['data_hex_formatted'] = [f'0x{sequence:04X}', f'0x{int.from_bytes(data_bytes, byteorder="little"):08X}']

        else:
            # 其他命令标准处理
            if len(raw_data) >= 9:
                data_length = raw_data[5]
                if data_length > 0 and len(raw_data) >= 9 + data_length:
                    data_start = 9  # 帧头(4) + 命令(1) + 数据长度(1) + 空字节占位(3) = 9
                    data_end = data_start + data_length
                    data_bytes = raw_data[data_start:data_end]

                    result['data_hex'] = data_bytes.hex().upper()

                    # 生成0x前缀的十六进制格式
                    if command == 'C0' and len(data_bytes) == 8:
                        # C0命令：8字节数据，按4字节一组分为两个32位值
                        input_io = int.from_bytes(data_bytes[0:4], byteorder='little')
                        output_io = int.from_bytes(data_bytes[4:8], byteorder='little')
                        result['data_hex_formatted'] = [f'0x{input_io:08X}', f'0x{output_io:08X}']
                    elif command in ['D0', 'D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8'] and len(data_bytes) % 2 == 0:
                        # D系列命令：按2字节一组分为16位值
                        result['data_hex_formatted'] = [f'0x{int.from_bytes(data_bytes[i:i+2], byteorder="little"):04X}' for i in range(0, len(data_bytes), 2)]
                    elif command == 'C5' and len(data_bytes) == 48:
                        # C5命令：版本信息查询，使用专门的解析函数
                        try:
                            version_info = self.parse_c5_version_info(data_bytes)
                            result['data'] = version_info
                            result['data_hex_formatted'] = []  # C5命令不使用标准的hex格式化

                            # 添加解析摘要信息
                            result['version_summary'] = {
                                'total_modules': len(version_info),
                                'valid_modules': sum(1 for info in version_info.values()
                                                   if info['version'] != '8.8' and info['date'] != '88-88-88'),
                                'invalid_modules': sum(1 for info in version_info.values()
                                                     if info['version'] == '8.8' or info['date'] == '88-88-88')
                            }

                            print(f"C5版本信息解析成功：共{result['version_summary']['total_modules']}个模块，"
                                  f"有效{result['version_summary']['valid_modules']}个，"
                                  f"无效{result['version_summary']['invalid_modules']}个")

                        except Exception as e:
                            print(f"C5版本信息解析失败: {e}")
                            result['data'] = None
                            result['error'] = f'C5版本信息解析失败: {e}'
                            # 降级为按字节显示
                            result['data_hex_formatted'] = [f'0x{b:02X}' for b in data_bytes]
                    elif command in ['C4', 'C3'] and len(data_bytes) % 4 == 0:
                        # C4/C3命令：按4字节一组分为32位值
                        result['data_hex_formatted'] = [f'0x{int.from_bytes(data_bytes[i:i+4], byteorder="little"):08X}' for i in range(0, len(data_bytes), 4)]
                    elif command == 'C1' and len(data_bytes) % 2 == 0:
                        # C1命令：按2字节一组分为16位值
                        result['data_hex_formatted'] = [f'0x{int.from_bytes(data_bytes[i:i+2], byteorder="little"):04X}' for i in range(0, len(data_bytes), 2)]
                    else:
                        # 其他情况：按字节显示
                        result['data_hex_formatted'] = [f'0x{b:02X}' for b in data_bytes]



        result['success'] = True
        return result




class RealSerialService:
    """真实串口通信服务"""

    def __init__(self):
        """初始化串口服务"""
        self.serial_port = None
        self.parser = RealProtocolParser()
        self.data_rotator = SimpleDataRotator()
        self.running = False

        # 命令序列定义
        self.startup_sequence = ['C0', '59', 'C5', 'D6', 'D7', 'D8']
        self.loop_sequence = ['C1', 'C4', 'D0', 'D1', 'D2', 'C4', 'D3', 'D4', 'D5', 'C4', 'C0']

        self.startup_completed = False
        self.in_startup_sequence = False
        self.current_loop_index = 0
        self.param_59_frames_received = 0
        self.param_59_total_frames = 259  # 根据实际运行报文，59命令会接收大量帧数据

        # 写入命令相关
        self.write_cmd_queue = []  # 写入命令队列
        self.write_cmd_file_path = "data/write_cmd_data.json"
        self.last_write_cmd_data = {}  # 上次读取的写入命令数据
        self.write_commands_map = {
            'A1': {'name': '启动', 'hex': 'A1'},
            'A2': {'name': '停止', 'hex': 'A2'},
            'A3': {'name': '复位', 'hex': 'A3'},
            'A4': {'name': '无功启动', 'hex': 'A4'},
            'A5': {'name': '谐波消除', 'hex': 'A5'},
            'A6': {'name': '综合控制', 'hex': 'A6'},
            'AA': {'name': '水冷启动', 'hex': 'AA'},
            'AB': {'name': '水冷停止', 'hex': 'AB'}
        }

    def load_config(self):
        """加载配置文件"""
        import platform
        
        # 根据操作系统选择配置文件
        config_file = 'config/service_windows.conf' if platform.system() == 'Windows' else 'config/service.conf'
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简单解析TOML格式
            config = {}
            for line in content.split('\n'):
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"')
                    config[key] = value

            # Windows系统默认使用COM3
            default_port = 'COM3' if platform.system() == 'Windows' else '/dev/ttyUSB0'
            
            return {
                'port': config.get('port', default_port),
                'baudrate': int(config.get('baudrate', '19200')),
                'timeout': float(config.get('timeout', '2.0'))
            }
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            # Windows系统默认使用COM3
            default_port = 'COM3' if platform.system() == 'Windows' else '/dev/ttyUSB0'
            return {
                'port': default_port,
                'baudrate': 19200,
                'timeout': 2.0
            }

    def load_write_cmd_data(self):
        """加载写入命令数据文件"""
        try:
            if os.path.exists(self.write_cmd_file_path):
                with open(self.write_cmd_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换为字典格式，以id为键
                    return {item['id']: item for item in data}
            return {}
        except Exception as e:
            print(f"加载写入命令数据失败: {e}")
            return {}

    def check_write_cmd_changes(self):
        """检查写入命令数据变化并添加到队列"""
        try:
            current_data = self.load_write_cmd_data()

            # 检查每个命令的value值变化
            for cmd_id, cmd_info in current_data.items():
                if cmd_id in self.write_commands_map:
                    current_value = cmd_info.get('value', '0')
                    last_value = self.last_write_cmd_data.get(cmd_id, {}).get('value', '0')

                    # 如果值从0变为1，或者当前值为1且队列中没有该命令，添加到写入命令队列
                    if (last_value == '0' and current_value == '1') or (current_value == '1' and cmd_id not in self.write_cmd_queue):
                        cmd_name = self.write_commands_map[cmd_id]['name']
                        print(f"检测到{cmd_name}命令触发，添加到写入队列: {cmd_id}")
                        if cmd_id not in self.write_cmd_queue:
                            self.write_cmd_queue.append(cmd_id)

            # 更新上次数据
            self.last_write_cmd_data = current_data

        except Exception as e:
            print(f"检查写入命令变化失败: {e}")

    def build_write_command(self, cmd_id: str) -> bytes:
        """构建写入命令帧

        写入命令帧结构：
        - 帧头：EB900101 (4字节)
        - 命令：A1/A2等 (1字节)
        - 8个占位字节：00000000 00000000 (8字节)
        - 校验：计算值 (1字节)
        - 帧尾：AAAB (2字节)
        总计：16字节
        """
        try:
            if cmd_id not in self.write_commands_map:
                raise ValueError(f"未知的写入命令: {cmd_id}")

            # 构建写入命令帧
            frame = bytearray([0xEB, 0x90, 0x01, 0x01])  # 帧头

            # 添加命令字节
            cmd_hex = self.write_commands_map[cmd_id]['hex']
            if len(cmd_hex) == 2:
                frame.append(int(cmd_hex, 16))
            else:
                raise ValueError(f"命令格式错误: {cmd_hex}")

            # 添加8个占位字节（写入命令特征）
            frame.extend([0x00] * 8)

            # 计算校验和
            checksum = self.parser.calculate_checksum(frame)
            frame.append(checksum)

            # 帧尾
            frame.extend([0xAA, 0xAB])

            return bytes(frame)

        except Exception as e:
            print(f"构建写入命令失败: {e}")
            return b''

    async def start(self):
        """启动串口服务"""
        print("启动真实串口通信服务...")

        config = self.load_config()

        try:
            # 尝试打开串口
            self.serial_port = serial.Serial(
                port=config['port'],
                baudrate=config['baudrate'],
                bytesize=8,
                parity='O',  # 奇校验
                stopbits=1,
                timeout=config['timeout']
            )
            print(f"串口 {config['port']} 打开成功")

        except Exception as e:
            print(f"串口打开失败: {e}")
            return

        self.running = True

        # 初始化写入命令数据（设置为空，以便第一次检查时能检测到已触发的命令）
        self.last_write_cmd_data = {}

        try:
            # 执行启动序列
            await self._execute_startup_sequence()

            # 执行循环序列
            while self.running:
                await self._execute_loop_sequence()

                # 检查写入命令队列并执行
                await self._check_and_execute_write_commands()

                await asyncio.sleep(0.1)  # 短暂延时

        except Exception as e:
            print(f"通信过程中发生错误: {e}")
        finally:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                print("串口已关闭")

    async def _execute_startup_sequence(self):
        """执行启动序列"""
        print("执行启动序列...")
        self.in_startup_sequence = True

        for i, command in enumerate(self.startup_sequence):
            if not self.running:
                break

            print(f"执行启动序列命令 {i+1}/{len(self.startup_sequence)}: {command}")
            await self._execute_command(command)
            print(f"启动序列命令 {command} 执行完成")
            await asyncio.sleep(0.2)  # 命令间延时

        self.startup_completed = True
        self.in_startup_sequence = False
        print("启动序列完成")

    async def _execute_loop_sequence(self):
        """执行循环序列"""
        if not self.startup_completed:
            return

        command = self.loop_sequence[self.current_loop_index]
        await self._execute_command(command)

        self.current_loop_index = (self.current_loop_index + 1) % len(self.loop_sequence)
        await asyncio.sleep(0.1)  # 循环命令间延时

    async def _check_and_execute_write_commands(self):
        """检查写入命令变化并执行队列中的命令"""
        # 检查JSON文件变化
        print("检查写入命令变化...")
        self.check_write_cmd_changes()

        # 执行队列中的所有写入命令
        if self.write_cmd_queue:
            print(f"写入命令队列中有 {len(self.write_cmd_queue)} 个命令待执行")
        while self.write_cmd_queue:
            cmd_id = self.write_cmd_queue.pop(0)  # 取出队列中的第一个命令
            await self._execute_write_command(cmd_id)

    async def _execute_write_command(self, cmd_id: str):
        """执行单个写入命令"""
        try:
            cmd_name = self.write_commands_map[cmd_id]['name']
            print(f"执行写入命令: {cmd_id} ({cmd_name})")

            # 构建命令帧
            command_frame = self.build_write_command(cmd_id)
            if not command_frame:
                print(f"构建写入命令失败: {cmd_id}")
                return

            # 发送命令
            print(f"发送写入命令 {cmd_id}: {command_frame.hex().upper()}")
            self.serial_port.write(command_frame)

            # 接收响应
            response = self._receive_response()
            if response:
                print(f"接收写入命令响应 {cmd_id}: {response.hex().upper()}")

                # 验证响应是否正确
                # 对于写入命令，检查响应的基本结构：帧头、命令、8个占位字节、校验、帧尾
                success = False
                if (len(response) == 16 and  # 写入命令响应固定16字节
                    response[:4] == b'\xEB\x90\x01\x01' and  # 帧头正确
                    response[4] == int(cmd_id, 16) and  # 命令字节正确
                    response[-2:] == b'\xAA\xAB'):  # 帧尾正确
                    success = True
                    print(f"写入命令 {cmd_id} ({cmd_name}) 执行成功 - 响应帧结构正确")
                else:
                    print(f"写入命令 {cmd_id} 响应不匹配")
                    print(f"期望: 16字节帧，帧头EB900101 + 命令{cmd_id} + 8个占位字节 + 校验 + 帧尾AAAB")
                    print(f"实际: {len(response)}字节，{response.hex().upper()}")

                # 保存执行结果
                result_data = {
                    'command': cmd_id,
                    'command_name': cmd_name,
                    'timestamp': time.time(),
                    'sent_hex': command_frame.hex().upper(),
                    'received_hex': response.hex().upper(),
                    'success': success,
                    'type': 'write_command'
                }

                if not success:
                    result_data['error'] = '响应不匹配'

                self.data_rotator.save_data(result_data)
            else:
                print(f"写入命令 {cmd_id} 未收到响应")
                result_data = {
                    'command': cmd_id,
                    'command_name': cmd_name,
                    'timestamp': time.time(),
                    'sent_hex': command_frame.hex().upper(),
                    'success': False,
                    'error': '未收到响应',
                    'type': 'write_command'
                }
                self.data_rotator.save_data(result_data)

            # 无论响应是否匹配，都重置value值（命令已发送）
            await self._reset_write_cmd_value(cmd_id)

            await asyncio.sleep(0.1)  # 写入命令间延时

        except Exception as e:
            print(f"执行写入命令失败 {cmd_id}: {e}")

    async def _reset_write_cmd_value(self, cmd_id: str):
        """重置写入命令的value值为0"""
        try:
            if os.path.exists(self.write_cmd_file_path):
                with open(self.write_cmd_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 找到对应的命令并重置value
                for item in data:
                    if item['id'] == cmd_id:
                        item['value'] = '0'
                        item['ts'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                        break

                # 写回文件
                with open(self.write_cmd_file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=4)

                print(f"已重置命令 {cmd_id} 的value值为0")

        except Exception as e:
            print(f"重置写入命令value值失败 {cmd_id}: {e}")

    async def _execute_command(self, command: str):
        """执行单个命令"""
        if not self.serial_port or not self.serial_port.is_open:
            return

        try:
            # 构建命令帧
            cmd_frame = self.parser.build_command(command)

            # 发送命令
            self.serial_port.write(cmd_frame)
            print(f"发送命令 {command}: {cmd_frame.hex().upper()}")

            # 清空接收缓冲区，避免读取到之前的数据
            self.serial_port.reset_input_buffer()

            # 接收响应
            if command == '59':  # 59命令需要接收多帧
                await self._receive_59_responses(command)
            else:
                # 增加重试机制
                max_retries = 3
                for retry in range(max_retries):
                    response = self._receive_response()
                    if response:
                        await self._process_response(command, response)
                        break
                    else:
                        print(f"命令 {command} 响应超时，重试 {retry+1}/{max_retries}")
                        await asyncio.sleep(0.1)  # 短暂延时后重试

        except Exception as e:
            print(f"命令 {command} 执行失败: {e}")

    async def _receive_59_responses(self, command: str):
        """接收59命令的多帧响应"""
        frames_received = 0

        while frames_received < self.param_59_total_frames and self.running:
            response = self._receive_response()
            if response:
                await self._process_response(command, response)
                frames_received += 1
            else:
                break  # 超时或无响应

            await asyncio.sleep(0.01)  # 帧间短暂延时

    def _receive_response(self) -> Optional[bytes]:
        """接收响应数据，增强版"""
        try:
            # 设置更短的超时时间来读取帧头，避免长时间阻塞
            original_timeout = self.serial_port.timeout
            self.serial_port.timeout = 0.5

            # 读取帧头
            header = self.serial_port.read(4)
            if len(header) != 4:
                print("接收超时：未收到完整帧头")
                self.serial_port.timeout = original_timeout
                return None

            # 如果收到的不是标准帧头，尝试在缓冲区中查找
            if header != b'\xEB\x90\x01\x01':
                print(f"收到非标准帧头: {header.hex().upper()}，尝试查找有效帧头")
                # 读取更多数据以查找帧头
                more_data = self.serial_port.read(20)  # 多读一些数据
                buffer = header + more_data
                header_index = buffer.find(b'\xEB\x90\x01\x01')

                if header_index >= 0:
                    print(f"在位置 {header_index} 找到有效帧头")
                    # 丢弃帧头前的数据
                    buffer = buffer[header_index:]
                    if len(buffer) < 4:
                        print("缓冲区中的帧头不完整")
                        self.serial_port.timeout = original_timeout
                        return None
                else:
                    print("未找到有效帧头")
                    self.serial_port.timeout = original_timeout
                    return None

                # 使用找到的帧头继续处理
                header = buffer[:4]
                buffer = buffer[4:]

                # 如果缓冲区中有足够的数据，直接从中读取命令和长度
                if len(buffer) >= 2:
                    cmd_and_length = buffer[:2]
                    buffer = buffer[2:]
                else:
                    # 否则从串口读取
                    cmd_and_length = self.serial_port.read(2)
            else:
                # 标准帧头，直接读取命令和长度
                cmd_and_length = self.serial_port.read(2)
                buffer = b''

            # 恢复原始超时设置
            self.serial_port.timeout = original_timeout

            if len(cmd_and_length) != 2:
                print("接收超时：未收到命令和长度字节")
                return None

            command_byte = cmd_and_length[0]
            second_byte = cmd_and_length[1]
            print(f"命令: {command_byte:02X}, 第二字节: {second_byte:02X}")

            # 判断是否为写入命令（A1-A6, AA, AB）
            is_write_command = command_byte in [0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xAA, 0xAB]

            if is_write_command:
                # 写入命令帧结构：帧头(4) + 命令(1) + 8个占位字节(8) + 校验(1) + 帧尾(2) = 16字节
                # 已读取：帧头(4) + 命令+第一个占位字节(2) = 6字节，剩余：7个占位字节 + 校验 + 帧尾 = 10字节
                remaining_length = 7 + 1 + 2  # 7个占位字节 + 校验 + 帧尾
                print(f"检测到写入命令，剩余读取长度: {remaining_length}")
            else:
                # 读取命令：第二字节是数据长度
                data_length = second_byte
                remaining_length = 3 + data_length + 1 + 2  # 3空字节 + 数据 + 校验 + 帧尾
                print(f"检测到读取命令，数据长度: {data_length}, 剩余读取长度: {remaining_length}")

            # 如果缓冲区中有数据，先使用缓冲区中的数据
            if buffer and len(buffer) > 0:
                if len(buffer) >= remaining_length:
                    # 缓冲区中有足够的数据
                    remaining_data = buffer[:remaining_length]
                    # 丢弃使用的数据
                    buffer = buffer[remaining_length:]
                else:
                    # 缓冲区中的数据不足，需要从串口读取剩余部分
                    remaining_data = buffer
                    bytes_to_read = remaining_length - len(buffer)
                    remaining_data += self.serial_port.read(bytes_to_read)
            else:
                # 直接从串口读取所有剩余数据
                remaining_data = self.serial_port.read(remaining_length)

            if len(remaining_data) != remaining_length:
                print(f"接收超时：预期读取 {remaining_length} 字节，实际读取 {len(remaining_data)} 字节")
                return None

            # 组合完整响应
            full_response = header + cmd_and_length + remaining_data
            return full_response

        except Exception as e:
            print(f"接收响应失败: {e}")
            return None

    async def _process_response(self, command: str, response: bytes):
        """处理响应数据"""
        print(f"接收响应 {command}: {response.hex().upper()}")

        # 解析响应
        parsed = self.parser.parse_response(command, response)

        # 处理命令不匹配的情况
        if not parsed['success'] and 'error' in parsed and '响应命令不匹配' in parsed['error']:
            # 提取实际收到的命令
            actual_cmd = parsed['error'].split('实际: ')[1] if '实际: ' in parsed['error'] else None
            if actual_cmd:
                print(f"收到不匹配的命令响应，尝试以实际命令 {actual_cmd} 重新解析")
                # 以实际命令重新解析
                parsed = self.parser.parse_response(actual_cmd, response)
                # 添加注释说明原始期望命令
                parsed['note'] = f'原期望命令: {command}, 实际收到: {actual_cmd}'

        if parsed['success']:
            print(f"命令 {command} 执行成功")

            # 对C5命令进行特殊处理，打印版本信息摘要
            if command == 'C5' and 'data' in parsed and parsed['data']:
                self._print_c5_version_summary(parsed['data'])
        else:
            print(f"命令 {command} 解析失败: {parsed.get('error', '未知错误')}")

        # 根据启动序列状态选择保存方法
        if self.in_startup_sequence:
            self.data_rotator.save_startup_data(parsed)
        else:
            self.data_rotator.save_data(parsed)

        # 同时保存所有命令数据到实时数据文件
        self.data_rotator.save_all_data(parsed)

    def _print_c5_version_summary(self, version_data: Dict[str, Any]):
        """
        打印C5版本信息摘要

        Args:
            version_data: 解析后的版本信息数据
        """
        print("\n" + "="*80)
        print("C5版本信息解析结果摘要")
        print("="*80)
        print(f"{'组号':<4} {'模块名称':<25} {'版本号':<8} {'修改日期':<12} {'原始字节'}")
        print("-"*80)

        for module_name, info in version_data.items():
            group_index = info.get('group_index', 0)
            version = info.get('version', 'N/A')
            date = info.get('date', 'N/A')
            raw_bytes = info.get('raw_bytes', 'N/A')

            # 标记无效数据
            status_mark = ""
            if version == "8.8" or date == "88-88-88":
                status_mark = " [无效]"
            elif version == "0.0" or date == "00-00-00":
                status_mark = " [未设置]"

            print(f"{group_index:<4} {module_name:<25} {version:<8} {date:<12} {raw_bytes}{status_mark}")

        print("="*80 + "\n")

    async def stop(self):
        """停止服务"""
        print("正在停止串口服务...")
        self.running = False

        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()

        print("串口服务已停止")


async def main():
    """主函数"""
    service = RealSerialService()

    try:
        await service.start()
    except KeyboardInterrupt:
        print("\n收到中断信号")
    finally:
        await service.stop()


if __name__ == "__main__":
    asyncio.run(main())