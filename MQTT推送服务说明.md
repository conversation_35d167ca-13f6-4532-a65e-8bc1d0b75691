# MQTT C0数据推送服务

## 功能说明

本服务用于将串口采集的C0解析数据通过MQTT协议推送到远程服务器。

**版本更新**: 参考FastBee_sdk.py进行了全面优化，提升了连接稳定性、错误处理和监控能力。

## 文件说明

- `mqtt_publisher.py`: MQTT推送服务主程序
- `docs/C0Mqtt推送`: MQTT服务器认证配置文件
- `data/c0_parsed_data.json`: C0解析数据文件（由串口服务生成）

## 配置信息

### MQTT服务器配置
- **服务器地址**: 121.196.192.8:1883
- **客户端ID**: S&D19QDKG1WYPE6&188&19
- **用户名**: bydq_admin
- **发布主题**: /188/D19QDKG1WYPE6/property/post
- **订阅主题**: /188/D19QDKG1WYPE6/function/get

## 运行方式

### 启动服务
```bash
cd /home/<USER>/SerialPortRule
python mqtt_publisher.py
```

### 停止服务
按 `Ctrl+C` 停止服务

## 服务特性

### 基础功能
- **自动连接管理**: 自动连接MQTT服务器并处理连接异常
- **数据格式化**: 将C0解析数据格式化为JSON格式
- **定期推送**: 支持定时推送功能（默认30秒间隔）
- **日志记录**: 完整的日志记录系统
- **异常处理**: 完善的异常处理机制

### 优化特性（参考FastBee_sdk.py）
- **智能重连机制**: 连接失败时自动重试，支持最大重试次数限制
- **连接状态监控**: 实时监控连接状态，提供详细的状态信息
- **健康检查**: 定期进行健康检查，及时发现并处理连接问题
- **线程安全**: 使用线程安全的定时器机制，避免资源竞争
- **消息订阅**: 支持订阅控制主题，可接收远程控制指令
- **优雅退出**: 程序退出时自动清理资源，确保安全关闭
- **状态回调**: 完善的MQTT事件回调处理机制

## 消息格式

推送的MQTT消息体格式如下：

```json
{
  "timestamp": "2025-08-19T15:23:18.823456",
  "device_id": "SVG_Controller",
  "message_type": "C0_IO_Status",
  "data_count": 64,
  "io_points": [
    {
      "id": "HMI_30018_0",
      "name": "启动按钮输入",
      "ts": "2025-08-19 14:55:43.329",
      "value": "0"
    },
    // ... 更多IO点数据
  ]
}
```

## 状态监控

服务运行时会输出详细的日志信息：

- `INFO`: 正常运行信息
- `WARNING`: 警告信息（如连接断开）
- `ERROR`: 错误信息（如连接失败）

## 依赖库

- `paho-mqtt`: MQTT客户端库
- `json`: JSON数据处理
- `pathlib`: 文件路径处理
- `logging`: 日志记录

## 注意事项

1. 确保网络连接正常，能够访问MQTT服务器
2. 确保`data/c0_parsed_data.json`文件存在且格式正确
3. 服务会持续运行，建议使用systemd等工具管理服务进程
4. 如需修改推送间隔，可在代码中调整`interval`参数