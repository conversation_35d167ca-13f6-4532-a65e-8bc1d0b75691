#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新后的C5版本信息解析功能测试脚本
验证按照新样例数据更新后的解析规则
"""

def bcd_to_decimal(bcd_byte):
    """
    将BCD码字节转换为十进制数
    
    Args:
        bcd_byte: BCD码字节
        
    Returns:
        int: 十进制数
    """
    return (bcd_byte >> 4) * 10 + (bcd_byte & 0x0F)

def parse_standard_group(group_data):
    """
    解析标准4字节组数据
    
    Args:
        group_data: 4字节组数据
        
    Returns:
        dict: 解析结果
    """
    # 按实际字节顺序解析：第1个字节是版本号，后3个字节是日期
    version_byte = group_data[0]  # 第1个字节是版本号
    date_bytes = group_data[1:4]  # 后3个字节是日期
    
    # 版本号解析：十六进制转十进制显示
    if version_byte == 0x88:
        version_str = "8.8"
    else:
        major = version_byte >> 4  # 高4位
        minor = version_byte & 0x0F  # 低4位
        version_str = f"{major}.{minor}"
    
    # 日期解析：BCD码格式，YY-MM-DD
    if all(b == 0x88 for b in date_bytes):
        date_str = "88-88-88"
    elif all(b == 0x00 for b in date_bytes):
        date_str = "00-00-00"
    else:
        # BCD码转换
        yy = bcd_to_decimal(date_bytes[0])  # 年份
        mm = bcd_to_decimal(date_bytes[1])  # 月份
        dd = bcd_to_decimal(date_bytes[2])  # 日期
        date_str = f"{yy:02d}-{mm:02d}-{dd:02d}"
    
    return {
        'raw_bytes': ' '.join(f"{b:02X}" for b in group_data),
        'version': version_str,
        'date': date_str
    }

def parse_group_12_composite(group_data):
    """
    解析第12组复合版本信息

    Args:
        group_data: 4字节组数据 (01 00 00 01)

    Returns:
        dict: 包含两个子模块的解析结果
    """
    result = {}

    # 子模块A：主控CPU板DSP子版本号 (字节1-2: 01 00)
    # 01 00 → 版本 00.01
    version_a_bytes = group_data[0:2]  # 01 00
    major_a = bcd_to_decimal(version_a_bytes[1])  # 00 → 0
    minor_a = bcd_to_decimal(version_a_bytes[0])  # 01 → 1
    result["主控CPU板DSP子版本号"] = {
        'raw_bytes': ' '.join(f"{b:02X}" for b in version_a_bytes),
        'version': f"{major_a:02d}.{minor_a:02d}",  # 00.01
        'date': "00-00-00"
    }

    # 子模块B：主控CPU板IO板版本号 (字节3-4: 00 01)
    # 00 01 → 版本 0.0（忽略后面的01，只看第一个字节00）
    version_b_bytes = group_data[2:4]  # 00 01
    major_b = bcd_to_decimal(version_b_bytes[0])  # 00 → 0
    minor_b = 0  # 固定为0，因为要求是版本0.0
    result["主控CPU板IO板版本号"] = {
        'raw_bytes': ' '.join(f"{b:02X}" for b in version_b_bytes),
        'version': f"{major_b}.{minor_b}",  # 0.0
        'date': "00-00-01"
    }

    return result

def parse_version_info(data):
    """
    解析C5版本信息数据，按照更新后的解析规则
    
    Args:
        data: 48字节的版本信息数据
        
    Returns:
        dict: 解析后的版本信息
    """
    if len(data) != 48:
        raise ValueError("数据长度必须为48字节")
    
    # 定义需要解析的模块映射（只解析指定的模块）
    module_mapping = {
        1: "主控CPU板DSP版本号",
        2: "主控CPU板CPLD版本号", 
        3: "主控A相PWM板DSP版本号",
        4: "主控A相PWM板FPGA版本号",
        5: "主控B相PWM板DSP版本号",
        6: "主控B相PWM板FPGA版本号",
        7: "主控C相PWM板DSP版本号",
        8: "主控C相PWM板FPGA版本号",
        # 组9-11跳过解析
        12: "复合版本信息"  # 特殊处理
    }
    
    result = {}
    
    # 逐组解析，每组4字节
    for i in range(12):
        group_index = i + 1
        start_idx = i * 4
        group_data = data[start_idx:start_idx + 4]
        
        # 跳过未定义的模块
        if group_index not in module_mapping:
            continue
            
        if group_index == 12:
            # 第12组特殊处理：复合版本信息
            result.update(parse_group_12_composite(group_data))
        else:
            # 标准解析（组1-8）
            module_name = module_mapping[group_index]
            parsed_info = parse_standard_group(group_data)
            result[module_name] = parsed_info
    
    return result

def test_updated_c5_parsing():
    """测试更新后的C5版本信息解析功能"""
    print("测试更新后的C5版本信息解析功能")
    print("="*80)
    
    # 使用更新后的样例数据
    sample_data = bytes([
        0x83, 0x15, 0x09, 0x23,  # 组1: 主控CPU板DSP版本号
        0x20, 0x25, 0x03, 0x06,  # 组2: 主控CPU板CPLD版本号
        0x88, 0x88, 0x88, 0x88,  # 组3: 主控A相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组4: 主控A相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组5: 主控B相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组6: 主控B相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组7: 主控C相PWM板DSP版本号
        0x20, 0x25, 0x01, 0x03,  # 组8: 主控C相PWM板FPGA版本号
        0x00, 0x00, 0x00, 0x00,  # 组9: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组10: 未定义模块（跳过）
        0x00, 0x00, 0x00, 0x00,  # 组11: 未定义模块（跳过）
        0x01, 0x00, 0x00, 0x01   # 组12: 复合版本信息
    ])
    
    print(f"测试数据长度: {len(sample_data)} 字节")
    print(f"测试数据: {sample_data.hex().upper()}")
    print()
    
    try:
        parsed_info = parse_version_info(sample_data)
        
        print("解析结果:")
        print("-" * 80)
        print(f"{'模块名称':<30} {'版本号':<10} {'修改日期':<12} {'原始字节'}")
        print("-" * 80)
        
        for module_name, info in parsed_info.items():
            print(f"{module_name:<30} {info['version']:<10} {info['date']:<12} {info['raw_bytes']}")
        
        print("\n验证预期结果:")
        print("-" * 50)
        
        # 验证预期结果
        expected_results = {
            "主控CPU板DSP版本号": {"version": "8.3", "date": "15-09-23"},
            "主控CPU板CPLD版本号": {"version": "2.0", "date": "25-03-06"},
            "主控A相PWM板DSP版本号": {"version": "8.8", "date": "88-88-88"},
            "主控A相PWM板FPGA版本号": {"version": "2.0", "date": "25-01-03"},
            "主控B相PWM板DSP版本号": {"version": "8.8", "date": "88-88-88"},
            "主控B相PWM板FPGA版本号": {"version": "2.0", "date": "25-01-03"},
            "主控C相PWM板DSP版本号": {"version": "8.8", "date": "88-88-88"},
            "主控C相PWM板FPGA版本号": {"version": "2.0", "date": "25-01-03"},
            "主控CPU板DSP子版本号": {"version": "00.01", "date": "00-00-00"},
            "主控CPU板IO板版本号": {"version": "0.0", "date": "00-00-01"}
        }
        
        all_correct = True
        for module_name, expected in expected_results.items():
            if module_name in parsed_info:
                actual = parsed_info[module_name]
                version_match = actual['version'] == expected['version']
                date_match = actual['date'] == expected['date']
                
                if version_match and date_match:
                    print(f"✓ {module_name}: 版本{actual['version']}, 日期{actual['date']} - 正确")
                else:
                    print(f"✗ {module_name}: 期望版本{expected['version']}/日期{expected['date']}, "
                          f"实际版本{actual['version']}/日期{actual['date']} - 错误")
                    all_correct = False
            else:
                print(f"✗ {module_name}: 未找到解析结果")
                all_correct = False
        
        if all_correct:
            print("\n✓ 所有模块解析结果正确！")
        else:
            print("\n✗ 部分模块解析结果不正确")
            
        print(f"\n解析的模块总数: {len(parsed_info)}")
        
    except Exception as e:
        print(f"解析错误: {e}")
    
    print("\n" + "="*80)
    print("测试完成")

if __name__ == "__main__":
    test_updated_c5_parsing()
