#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C5版本信息解析功能测试脚本
用于验证C5命令响应数据解析功能是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from simple_main import RealProtocolParser


def test_c5_version_parsing():
    """测试C5版本信息解析功能"""
    print("开始测试C5版本信息解析功能...")
    
    # 创建协议解析器实例
    parser = RealProtocolParser()
    
    # 测试数据1：文档中的示例数据
    print("\n" + "="*80)
    print("测试1：使用文档示例数据")
    print("="*80)
    
    # 构建完整的C5响应帧（60字节）
    # 帧头(4) + 命令(1) + 数据长度(1) + 空字节(3) + 版本数据(48) + 校验(1) + 帧尾(2) = 60字节
    sample_frame = bytearray([
        # 帧头
        0xEB, 0x90, 0x01, 0x01,
        # 命令
        0xC5,
        # 数据长度
        0x30,  # 48字节
        # 空字节占位
        0x00, 0x00, 0x00,
        # 版本数据（48字节）
        0x23, 0x09, 0x15, 0x83,  # 组1: 主控CPU板DSP版本号
        0x06, 0x03, 0x25, 0x20,  # 组2: 主控CPU板CPLD版本号
        0x88, 0x88, 0x88, 0x88,  # 组3: 主控A相PWM板DSP版本号
        0x03, 0x01, 0x25, 0x20,  # 组4: 主控A相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组5: 主控B相PWM板DSP版本号
        0x03, 0x01, 0x25, 0x20,  # 组6: 主控B相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组7: 主控C相PWM板DSP版本号
        0x03, 0x01, 0x25, 0x20,  # 组8: 主控C相PWM板FPGA版本号
        0x88, 0x88, 0x88, 0x88,  # 组9: 主控COMM板DSP版本号
        0x88, 0x88, 0x88, 0x88,  # 组10: 主控COMM板CPLD版本号
        0x00, 0x00, 0x00, 0x00,  # 组11: 主控CPU板FPGA版本号
        0x01, 0x00, 0x00, 0x01   # 组12: 主控CPU板子版本号
    ])
    
    # 计算校验和
    checksum = parser.calculate_checksum(sample_frame)
    sample_frame.append(checksum)
    
    # 帧尾
    sample_frame.extend([0xAA, 0xAB])
    
    # 转换为bytes
    sample_frame = bytes(sample_frame)
    
    print(f"测试帧长度: {len(sample_frame)} 字节")
    print(f"测试帧数据: {sample_frame.hex().upper()}")
    
    # 解析响应
    result = parser.parse_response('C5', sample_frame)
    
    if result['success']:
        print("✓ C5响应解析成功")
        
        if 'data' in result and result['data']:
            version_data = result['data']
            print(f"✓ 版本信息解析成功，共解析{len(version_data)}个模块")
            
            # 验证预期结果
            expected_results = {
                "主控CPU板DSP版本号": {"version": "8.3", "date": "15-09-23"},
                "主控CPU板CPLD版本号": {"version": "2.0", "date": "25-03-06"},
                "主控A相PWM板DSP版本号": {"version": "8.8", "date": "88-88-88"},
                "主控A相PWM板FPGA版本号": {"version": "2.0", "date": "25-01-03"},
                "主控CPU板FPGA版本号": {"version": "0.0", "date": "00-00-00"},
                "主控CPU板子版本号": {"version": "0.1", "date": "00-00-01"}
            }
            
            print("\n验证关键模块解析结果:")
            all_correct = True
            for module_name, expected in expected_results.items():
                if module_name in version_data:
                    actual = version_data[module_name]
                    version_match = actual['version'] == expected['version']
                    date_match = actual['date'] == expected['date']

                    # 调试信息：显示原始字节
                    raw_bytes = actual.get('raw_bytes', 'N/A')
                    print(f"调试 - {module_name}: 原始字节={raw_bytes}")

                    if version_match and date_match:
                        print(f"✓ {module_name}: 版本{actual['version']}, 日期{actual['date']} - 正确")
                    else:
                        print(f"✗ {module_name}: 期望版本{expected['version']}/日期{expected['date']}, "
                              f"实际版本{actual['version']}/日期{actual['date']} - 错误")
                        all_correct = False
                else:
                    print(f"✗ {module_name}: 未找到解析结果")
                    all_correct = False
            
            if all_correct:
                print("✓ 所有关键模块解析结果正确")
            else:
                print("✗ 部分模块解析结果不正确")
                
            # 显示摘要信息
            if 'version_summary' in result:
                summary = result['version_summary']
                print(f"\n版本信息摘要:")
                print(f"  总模块数: {summary['total_modules']}")
                print(f"  有效模块: {summary['valid_modules']}")
                print(f"  无效模块: {summary['invalid_modules']}")
        else:
            print("✗ 版本信息解析失败，未获取到版本数据")
    else:
        print(f"✗ C5响应解析失败: {result.get('error', '未知错误')}")
    
    # 测试数据2：边界情况测试
    print("\n" + "="*80)
    print("测试2：边界情况测试")
    print("="*80)
    
    # 测试数据长度不正确的情况
    try:
        parser.parse_c5_version_info(b'\x00' * 47)  # 47字节，少1字节
        print("✗ 应该抛出长度错误异常")
    except ValueError as e:
        print(f"✓ 正确处理长度错误: {e}")
    
    # 测试全0数据
    try:
        zero_data = b'\x00' * 48
        result = parser.parse_c5_version_info(zero_data)
        print("✓ 全0数据解析成功")
        
        # 检查是否所有模块都显示为0.0和00-00-00
        all_zero = all(info['version'] == '0.0' and info['date'] == '00-00-00' 
                      for info in result.values())
        if all_zero:
            print("✓ 全0数据解析结果正确")
        else:
            print("✗ 全0数据解析结果不正确")
    except Exception as e:
        print(f"✗ 全0数据解析失败: {e}")
    
    # 测试全0x88数据
    try:
        invalid_data = b'\x88' * 48
        result = parser.parse_c5_version_info(invalid_data)
        print("✓ 全0x88数据解析成功")
        
        # 检查是否所有模块都显示为8.8和88-88-88
        all_invalid = all(info['version'] == '8.8' and info['date'] == '88-88-88' 
                         for info in result.values())
        if all_invalid:
            print("✓ 全0x88数据解析结果正确")
        else:
            print("✗ 全0x88数据解析结果不正确")
    except Exception as e:
        print(f"✗ 全0x88数据解析失败: {e}")
    
    print("\n" + "="*80)
    print("C5版本信息解析功能测试完成")
    print("="*80)


if __name__ == "__main__":
    test_c5_version_parsing()
