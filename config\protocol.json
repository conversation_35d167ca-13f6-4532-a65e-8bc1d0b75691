{"frame_header": "EB,90,01,01", "frame_tail": "AA,AB", "commands": {"C0": {"name": "IO状态查询", "command": "C0", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xC0", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x3D", "0xAA", "0xAB"], "response_length": 20, "data_length": 8, "data_type": "uint32", "little_endian": true, "description": "前4字节输入IO状态，后4字节输出IO状态，小端模式DCBA"}, "C1": {"name": "故障报警查询", "command": "C1", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xC1", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x30", "0xAA", "0xAB"], "response_length": 80, "data_length": 68, "data_type": "uint16", "little_endian": true, "description": "34个故障报警状态，每个状态2字节，小端模式BA"}, "C3": {"name": "故障录波查询", "command": "C3", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xC3", "0xC8", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x5A", "0xAA", "0xAB"], "response_length": 212, "data_length": 200, "data_type": "float32", "little_endian": true, "description": "50个浮点数录波数据，每个数据4字节，小端模式DCBA"}, "C4": {"name": "DSP状态查询", "command": "C4", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xC4", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x31", "0xAA", "0xAB"], "response_length": 204, "data_length": 192, "data_type": "float32", "little_endian": true, "description": "48个浮点数DSP状态数据，每个数据4字节，小端模式DCBA"}, "C5": {"name": "版本信息查询", "command": "C5", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xC5", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x42", "0xAA", "0xAB"], "response_length": 60, "data_length": 48, "data_type": "uint32", "little_endian": true, "description": "12个版本信息数据，每个数据4字节，小端模式DCBA，包含主控CPU板、PWM板、COMM板等各模块版本", "version_fields": ["主控CPU板DSP版本号", "主控CPU板CPLD版本号", "主控A相PWM板DSP版本号", "主控A相PWM板FPGA版本号", "主控B相PWM板DSP版本号", "主控B相PWM板FPGA版本号", "主控C相PWM板DSP版本号", "主控C相PWM板FPGA版本号", "主控COMM板DSP版本号", "主控COMM板CPLD版本号", "主控CPU板FPGA版本号", "主控CPU板子版本号"], "invalid_version_marker": "0x88888888"}, "59": {"name": "调试参数查询", "command": "59", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0x59", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0xE8", "0xAA", "0xAB"], "response_length": 16, "data_length": 4, "data_type": "float32", "little_endian": true, "description": "查询调试参数值，返回1个浮点数，4字节，小端模式DCBA"}, "57": {"name": "调试参数修改", "command": "57", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0x57", "0x04", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0xEA", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "参数修改命令，响应为55命令"}, "55": {"name": "调试参数修改响应", "command": "55", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0x55", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0xE2", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "57命令的响应帧，帧头+命令+空字节(2)+序号(2)+空字节(4)+校验+帧尾"}, "37": {"name": "修改完成确认", "command": "37", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0x37", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0xB4", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "修改完成确认命令，响应为5B命令"}, "5B": {"name": "修改完成确认响应", "command": "5B", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0x5B", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0xE8", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "37命令的响应帧，帧头+命令+空字节(8)+校验+帧尾"}, "D0": {"name": "A相单元直流侧电压查询", "command": "D0", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD0", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x3D", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个A相单元的直流侧电压，每个单元2字节，小端模式BA"}, "D1": {"name": "B相单元直流侧电压查询", "command": "D1", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD1", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x3E", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个B相单元的直流侧电压，每个单元2字节，小端模式BA"}, "D2": {"name": "C相单元直流侧电压查询", "command": "D2", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD2", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x3F", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个C相单元的直流侧电压，每个单元2字节，小端模式BA"}, "D3": {"name": "A相单元状态查询", "command": "D3", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD3", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x40", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个A相单元的状态信息，每个单元2字节，小端模式BA"}, "D4": {"name": "B相单元状态查询", "command": "D4", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD4", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x41", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个B相单元的状态信息，每个单元2字节，小端模式BA"}, "D5": {"name": "C相单元状态查询", "command": "D5", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD5", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x42", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个C相单元的状态信息，每个单元2字节，小端模式BA"}, "D6": {"name": "A相单元程序版本信息查询", "command": "D6", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD6", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x43", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个单元的版本信息，每个单元2字节，小端模式BA"}, "D7": {"name": "B相单元程序版本信息查询", "command": "D7", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD7", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x44", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个单元的版本信息，每个单元2字节，小端模式BA"}, "D8": {"name": "C相单元程序版本信息查询", "command": "D8", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xD8", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x45", "0xAA", "0xAB"], "response_length": 48, "data_length": 36, "data_type": "uint16", "little_endian": true, "description": "18个单元的版本信息，每个单元2字节，小端模式BA"}, "AA": {"name": "水冷系统启动", "command": "AA", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xAA", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x37", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "水冷系统启动命令，基于触摸屏宏64功能"}, "AB": {"name": "水冷系统停止", "command": "AB", "query_frame": ["0xEB", "0x90", "0x01", "0x01", "0xAB", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x00", "0x38", "0xAA", "0xAB"], "response_length": 16, "data_length": 0, "data_type": "none", "little_endian": true, "description": "水冷系统停止命令，基于触摸屏宏65功能"}}, "calculations": {"reactive_power": "(ab_voltage * 10 * reactive_current * rated_current * rated_voltage * sqrt(3)) / 10000000", "active_power": "(ab_voltage * 10 * active_current * rated_current * rated_voltage * sqrt(3)) / 10000000", "power_factor": "active_power / sqrt(active_power**2 + reactive_power**2)"}}