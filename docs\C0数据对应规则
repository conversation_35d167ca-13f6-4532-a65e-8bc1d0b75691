将json中C0命令中的 "data_hex_formatted": ["0x00000000", "0x00001016"]数据对应关系如下：
0x00000000 表示32位IO输入，对应如下：
启动按钮输入	HMI_30018_0
停止按钮输入	HMI_30018_1
复位按钮输入	HMI_30018_2
备用按钮输入	HMI_30018_3
急停按钮输入	HMI_30018_4
SVG断路器输入	HMI_30018_5
启动柜接触器输入	HMI_30018_6
启动柜隔离刀闸输入	HMI_30018_7
启动柜接地刀闸输入	HMI_30018_8
远方旋钮输入	HMI_30018_9
就地旋钮输入	HMI_30018_10
水冷系统电源故障输入	HMI_30018_11
水冷系统预警输入	HMI_30018_12
水冷系统请求跳闸输入	HMI_30018_13
水冷系统请求停止输入	HMI_30018_14
水冷系统运行/停止输入	HMI_30018_15
母联1状态输入	HMI_30019_0
母联2状态输入	HMI_30019_1
风机运行状态输入	HMI_30019_2
备用	HMI_30019_3
输入21	HMI_30019_4
输入22	HMI_30019_5
输入23	HMI_30019_6
输入24	HMI_30019_7
输入25	HMI_30019_8
输入26	HMI_30019_9
输入27	HMI_30019_10
输入28	HMI_30019_11
输入29	HMI_30019_12
输入30	HMI_30019_13
输入31	HMI_30019_14
输入32	HMI_30019_15
0x00001016 表示32位IO输出，对应如下：
SVG断路器允许合闸输出	HMI_30020_0
SVG断路器合闸命令输出	HMI_30020_1
SVG断路器分闸命令输出	HMI_30020_2
启动柜接触器合闸命令输出	HMI_30020_3
启动柜接触器分闸命令输出	HMI_30020_4
运行指示灯输出	HMI_30020_5
故障指示灯输出	HMI_30020_6
就绪指示灯输出	HMI_30020_7
报警指示灯输出	HMI_30020_8
保留指示灯输出	HMI_30020_9
SVG运行去水冷输出	HMI_30020_10
水冷启动输出	HMI_30020_11
水冷停止输出	HMI_30020_12
远程故障输出	HMI_30020_13
风机合闸输出	HMI_30020_14
故障报警脉冲输出	HMI_30020_15
输出17	HMI_30021_0
输出18	HMI_30021_1
输出19	HMI_30021_2
输出20	HMI_30021_3
输出21	HMI_30021_4
输出22	HMI_30021_5
输出23	HMI_30021_6
输出24	HMI_30021_7
输出25	HMI_30021_8
输出26	HMI_30021_9
输出27	HMI_30021_10
输出28	HMI_30021_11
输出29	HMI_30021_12
输出30	HMI_30021_13
输出31	HMI_30021_14
输出32	HMI_30021_15

保存成以下类似的Json格式：

保留转义
[
    {
        "id": "HMI_30005",
        "name": "谐波消除",
        "ts": "2025-08-19 11:41:33.655",
        "value": "0"
    },
    {
        "id": "HMI_30006",
        "name": "综合控制",
        "ts": "2025-08-19 11:41:33.655",
        "value": "0"
    },
    {
        "id": "HMI_31223_0",
        "name": "手动给定无功",
        "ts": "2025-08-19 11:41:33.655",
        "value": "0"
    },
    {
        "id": "HMI_31224_9",
        "name": "运行模式",
        "ts": "2025-08-19 11:41:33.655",
        "value": "0"
    },
    {
        "id": "HMI_30004",
        "name": "无功启动",
        "ts": "2025-08-19 11:41:33.655",
        "value": "0"
    }
]