{"command": "C1", "timestamp": 1755773873.8610892, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773874.2524223, "raw_hex": "EB900101C4C000000017672438C5B83A3993851539A2105BB95E4E363820F1653610AAFA36A8209B36C545FD33ABC868B35176D02E55B1683360BDE73B5D144A3625F01DBA8DD3DA390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000008F0A4B367364B9B6000000000000000000000000000000008DFE2B400000803F636F6E369CE91536E41CB135AB0FF8351F78223833F73939E22615390000000020F1653610AAFA36A8209B36E4866536DC69FA368C0A9B36ACAAAB", "success": true, "data": null, "data_hex": "17672438C5B83A3993851539A2105BB95E4E363820F1653610AAFA36A8209B36C545FD33ABC868B35176D02E55B1683360BDE73B5D144A3625F01DBA8DD3DA390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000008F0A4B367364B9B6000000000000000000000000000000008DFE2B400000803F636F6E369CE91536E41CB135AB0FF8351F78223833F73939E22615390000000020F1653610AAFA36A8209B36E4866536DC69FA368C0A9B36", "data_hex_formatted": ["0x38246717", "0x393AB8C5", "0x39158593", "0xB95B10A2", "0x38364E5E", "0x3665F120", "0x36FAAA10", "0x369B20A8", "0x33FD45C5", "0xB368C8AB", "0x2ED07651", "0x3368B155", "0x3BE7BD60", "0x364A145D", "0xBA1DF025", "0x39DAD38D", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x364B0A8F", "0xB6B96473", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x366E6F63", "0x3615E99C", "0x35B11CE4", "0x35F80FAB", "0x3822781F", "0x3939F733", "0x391526E2", "0x00000000", "0x3665F120", "0x36FAAA10", "0x369B20A8", "0x366586E4", "0x36FA69DC", "0x369B0A8C"]}
{"command": "D0", "timestamp": 1755773874.5068521, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773874.7608945, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773875.0288138, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773875.4228604, "raw_hex": "EB900101C4C0000000861E1A38B4124339CAD7213913984139546999B8209B6D36B8E401370A289C3619E1923444DDBAB46EA9D72C7BDCBA3460BDE73B754934352877013A04220DBA0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000003403BBB54228B8B6000000000000000000000000000000008DFE2B400000803FAF034E363408FC35CA37AF35FA6CC4355050193869D642398393213900000000209B6D36B8E401370A289C364A7E6D36C76B0137B55E9A3630AAAB", "success": true, "data": null, "data_hex": "861E1A38B4124339CAD7213913984139546999B8209B6D36B8E401370A289C3619E1923444DDBAB46EA9D72C7BDCBA3460BDE73B754934352877013A04220DBA0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000003403BBB54228B8B6000000000000000000000000000000008DFE2B400000803FAF034E363408FC35CA37AF35FA6CC4355050193869D642398393213900000000209B6D36B8E401370A289C364A7E6D36C76B0137B55E9A36", "data_hex_formatted": ["0x381A1E86", "0x394312B4", "0x3921D7CA", "0x39419813", "0xB8996954", "0x366D9B20", "0x3701E4B8", "0x369C280A", "0x3492E119", "0xB4BADD44", "0x2CD7A96E", "0x34BADC7B", "0x3BE7BD60", "0x35344975", "0x3A017728", "0xBA0D2204", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB5BB0334", "0xB6B82842", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x364E03AF", "0x35FC0834", "0x35AF37CA", "0x35C46CFA", "0x38195050", "0x3942D669", "0x39219383", "0x00000000", "0x366D9B20", "0x3701E4B8", "0x369C280A", "0x366D7E4A", "0x37016BC7", "0x369A5EB5"]}
{"command": "D3", "timestamp": 1755773875.6796768, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773875.9481983, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773876.2144651, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000100010078AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000001000100", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0001", "0x0001"]}
{"command": "C4", "timestamp": 1755773876.6061666, "raw_hex": "EB900101C4C0000000922D2638774B333977CC0E391E12B238003E3C39D9D17A36F0210637DF19A2366F74F4B316468E348BBD632D92468EB460BDE73B957288369C95133AD8F6E4390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000003246A03682B47B36000000000000000000000000000000008DFE2B400000803FB2C56036556A08363514B53540E2E835FB69233809253339C4830E3900000000D9D17A36F0210637DF19A236B0A87A3648D205378F83A136C2AAAB", "success": true, "data": null, "data_hex": "922D2638774B333977CC0E391E12B238003E3C39D9D17A36F0210637DF19A2366F74F4B316468E348BBD632D92468EB460BDE73B957288369C95133AD8F6E4390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000003246A03682B47B36000000000000000000000000000000008DFE2B400000803FB2C56036556A08363514B53540E2E835FB69233809253339C4830E3900000000D9D17A36F0210637DF19A236B0A87A3648D205378F83A136", "data_hex_formatted": ["0x38262D92", "0x39334B77", "0x390ECC77", "0x38B2121E", "0x393C3E00", "0x367AD1D9", "0x370621F0", "0x36A219DF", "0xB3F4746F", "0x348E4616", "0x2D63BD8B", "0xB48E4692", "0x3BE7BD60", "0x36887295", "0x3A13959C", "0x39E4F6D8", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x36A04632", "0x367BB482", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3660C5B2", "0x36086A55", "0x35B51435", "0x35E8E240", "0x382369FB", "0x39332509", "0x390E83C4", "0x00000000", "0x367AD1D9", "0x370621F0", "0x36A219DF", "0x367AA8B0", "0x3705D248", "0x36A1838F"]}
{"command": "C0", "timestamp": 1755773876.8489106, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773877.1439946, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773877.5374987, "raw_hex": "EB900101C4C0000000DA551A38CA2B34394A2612394C2680B77C8D38B9EA656336E637FB36BFF99836735DAF3302B9233438EAC02D7EB623B460BDE73B7BC4713603B2D7B9EF3C3CBA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CDAB1834BF8C9135000000000000000000000000000000008DFE2B400000803FB3E55336742E0C366412AF356E16D935602D1A388B2634395725123900000000EA656336E637FB36BFF9983669AF62364EF8FA36BFF598369EAAAB", "success": true, "data": null, "data_hex": "DA551A38CA2B34394A2612394C2680B77C8D38B9EA656336E637FB36BFF99836735DAF3302B9233438EAC02D7EB623B460BDE73B7BC4713603B2D7B9EF3C3CBA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CDAB1834BF8C9135000000000000000000000000000000008DFE2B400000803FB3E55336742E0C366412AF356E16D935602D1A388B2634395725123900000000EA656336E637FB36BFF9983669AF62364EF8FA36BFF59836", "data_hex_formatted": ["0x381A55DA", "0x39342BCA", "0x3912264A", "0xB780264C", "0xB9388D7C", "0x366365EA", "0x36FB37E6", "0x3698F9BF", "0x33AF5D73", "0x3423B902", "0x2DC0EA38", "0xB423B67E", "0x3BE7BD60", "0x3671C47B", "0xB9D7B203", "0xBA3C3CEF", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x3418ABCD", "0x35918CBF", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3653E5B3", "0x360C2E74", "0x35AF1264", "0x35D9166E", "0x381A2D60", "0x3934268B", "0x39122557", "0x00000000", "0x366365EA", "0x36FB37E6", "0x3698F9BF", "0x3662AF69", "0x36FAF84E", "0x3698F5BF"]}
{"command": "D0", "timestamp": 1755773877.7929864, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773878.0468156, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773878.3155851, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773878.7074187, "raw_hex": "EB900101C4C000000005590D38E21D303979A11139A67D0D38620D46396BF47E36D03C0A37D859A336EB1B06B490E35C33D48367AEF5C75CB360BDE73B00037F353731D239B03B273A0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000005F97E341B979DB6000000000000000000000000000000008DFE2B400000803FA9C76F36582E11362B409E359B12DE35ECF40B38BB892F39634E1139000000006BF47E36D03C0A37D859A3363CA37D36AFF309371801A33656AAAB", "success": true, "data": null, "data_hex": "05590D38E21D303979A11139A67D0D38620D46396BF47E36D03C0A37D859A336EB1B06B490E35C33D48367AEF5C75CB360BDE73B00037F353731D239B03B273A0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000005F97E341B979DB6000000000000000000000000000000008DFE2B400000803FA9C76F36582E11362B409E359B12DE35ECF40B38BB892F39634E1139000000006BF47E36D03C0A37D859A3363CA37D36AFF309371801A336", "data_hex_formatted": ["0x380D5905", "0x39301DE2", "0x3911A179", "0x380D7DA6", "0x39460D62", "0x367EF46B", "0x370A3CD0", "0x36A359D8", "0xB4061BEB", "0x335CE390", "0xAE6783D4", "0xB35CC7F5", "0x3BE7BD60", "0x357F0300", "0x39D23137", "0x3A273BB0", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x347EF905", "0xB69D971B", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x366FC7A9", "0x36112E58", "0x359E402B", "0x35DE129B", "0x380BF4EC", "0x392F89BB", "0x39114E63", "0x00000000", "0x367EF46B", "0x370A3CD0", "0x36A359D8", "0x367DA33C", "0x3709F3AF", "0x36A30118"]}
{"command": "D3", "timestamp": 1755773878.9629424, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773879.2317417, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773879.4978278, "raw_hex": "EB900101D5240000000000000000000000000000000000000000000000000000000000000000000000040004007EAAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000004000400", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0004", "0x0004"]}
{"command": "C4", "timestamp": 1755773879.876714, "raw_hex": "EB900101C4C0000000B5D93D38A6F53E39503B1239E0CCECB6776D70391D747E36F97B0737E7D39E36E9DE91B3DC15B2B4F647CD2E0B2CB23460BDE73BBCB4C43520495B39B40A3D3A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000001C8330B63219EE36000000000000000000000000000000008DFE2B400000803F575C5436EABAF935F166A335F93DBE35187C3D38F0273E391B7D1139000000001D747E36F97B0737E7D39E36CAA47D364B4D0737447C9E360AAAAB", "success": true, "data": null, "data_hex": "B5D93D38A6F53E39503B1239E0CCECB6776D70391D747E36F97B0737E7D39E36E9DE91B3DC15B2B4F647CD2E0B2CB23460BDE73BBCB4C43520495B39B40A3D3A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000001C8330B63219EE36000000000000000000000000000000008DFE2B400000803F575C5436EABAF935F166A335F93DBE35187C3D38F0273E391B7D1139000000001D747E36F97B0737E7D39E36CAA47D364B4D0737447C9E36", "data_hex_formatted": ["0x383DD9B5", "0x393EF5A6", "0x39123B50", "0xB6ECCCE0", "0x39706D77", "0x367E741D", "0x37077BF9", "0x369ED3E7", "0xB391DEE9", "0xB4B215DC", "0x2ECD47F6", "0x34B22C0B", "0x3BE7BD60", "0x35C4B4BC", "0x395B4920", "0x3A3D0AB4", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB630831C", "0x36EE1932", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36545C57", "0x35F9BAEA", "0x35A366F1", "0x35BE3DF9", "0x383D7C18", "0x393E27F0", "0x39117D1B", "0x00000000", "0x367E741D", "0x37077BF9", "0x369ED3E7", "0x367DA4CA", "0x37074D4B", "0x369E7C44"]}
{"command": "C0", "timestamp": 1755773880.1207554, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773880.4159548, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773880.808449, "raw_hex": "EB900101C4C000000052443F381810383906410C39951315B8CBDF4C39CB356136CCEE023776439F36E702453492F7C7B3C21F39AEFFF2C73360BDE73B79DCD03591CB723992584B3A0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000082EEA2B6D9466EB6000000000000000000000000000000008DFE2B400000803FF0596A36FF66E3355D4BA4354D98D6352D813D38B9AE3739DF110C3900000000CB356136CCEE023776439F36AE4A603673B90237DA309F365AAAAB", "success": true, "data": null, "data_hex": "52443F381810383906410C39951315B8CBDF4C39CB356136CCEE023776439F36E702453492F7C7B3C21F39AEFFF2C73360BDE73B79DCD03591CB723992584B3A0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000082EEA2B6D9466EB6000000000000000000000000000000008DFE2B400000803FF0596A36FF66E3355D4BA4354D98D6352D813D38B9AE3739DF110C3900000000CB356136CCEE023776439F36AE4A603673B90237DA309F36", "data_hex_formatted": ["0x383F4452", "0x39381018", "0x390C4106", "0xB8151395", "0x394CDFCB", "0x366135CB", "0x3702EECC", "0x369F4376", "0x344502E7", "0xB3C7F792", "0xAE391FC2", "0x33C7F2FF", "0x3BE7BD60", "0x35D0DC79", "0x3972CB91", "0x3A4B5892", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB6A2EE82", "0xB66E46D9", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x366A59F0", "0x35E366FF", "0x35A44B5D", "0x35D6984D", "0x383D812D", "0x3937AEB9", "0x390C11DF", "0x00000000", "0x366135CB", "0x3702EECC", "0x369F4376", "0x36604AAE", "0x3702B973", "0x369F30DA"]}
{"command": "D0", "timestamp": 1755773881.0628235, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773881.3168488, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773881.5857882, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773881.9782624, "raw_hex": "EB900101C4C0000000D12C213816EB333902EB103960E3E437C86BF2B83A807E3671430F373651AF367F7F07B23826B134A725442EE624B1B460BDE73B2831A235109356B998E932BA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000976A15B612D30636000000000000000000000000000000008DFE2B400000803F2C066C36DAF3D13515038E357BECA435F2252038F3E63339D2C01039000000003A807E3671430F373651AF36AAEE7C3694E70E37EA33AF36A5AAAB", "success": true, "data": null, "data_hex": "D12C213816EB333902EB103960E3E437C86BF2B83A807E3671430F373651AF367F7F07B23826B134A725442EE624B1B460BDE73B2831A235109356B998E932BA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000976A15B612D30636000000000000000000000000000000008DFE2B400000803F2C066C36DAF3D13515038E357BECA435F2252038F3E63339D2C01039000000003A807E3671430F373651AF36AAEE7C3694E70E37EA33AF36", "data_hex_formatted": ["0x38212CD1", "0x3933EB16", "0x3910EB02", "0x37E4E360", "0xB8F26BC8", "0x367E803A", "0x370F4371", "0x36AF5136", "0xB2077F7F", "0x34B12638", "0x2E4425A7", "0xB4B124E6", "0x3BE7BD60", "0x35A23128", "0xB9569310", "0xBA32E998", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB6156A97", "0x3606D312", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x366C062C", "0x35D1F3DA", "0x358E0315", "0x35A4EC7B", "0x382025F2", "0x3933E6F3", "0x3910C0D2", "0x00000000", "0x367E803A", "0x370F4371", "0x36AF5136", "0x367CEEAA", "0x370EE794", "0x36AF33EA"]}
{"command": "D3", "timestamp": 1755773882.2340662, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773882.5018692, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773882.7673793, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000700070084AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000007000700", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0007", "0x0007"]}
{"command": "C4", "timestamp": 1755773883.1609101, "raw_hex": "EB900101C4C0000000D8812C382C4D3B39500B143900F2E737D7C174B9784D77361396FC36C0519236FFC100B52FF61434C757542D62F714B460BDE73BBA0A4436C9835FB965BB3CBA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000731A83364739EF32000000000000000000000000000000008DFE2B400000803FED0D4736F337FB35AE84B1353250C135DD5E2C38AA073B3952CE133900000000784D77361396FC36C0519236D5C47436D0A8FA3638AC913664AAAB", "success": true, "data": null, "data_hex": "D8812C382C4D3B39500B143900F2E737D7C174B9784D77361396FC36C0519236FFC100B52FF61434C757542D62F714B460BDE73BBA0A4436C9835FB965BB3CBA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000731A83364739EF32000000000000000000000000000000008DFE2B400000803FED0D4736F337FB35AE84B1353250C135DD5E2C38AA073B3952CE133900000000784D77361396FC36C0519236D5C47436D0A8FA3638AC9136", "data_hex_formatted": ["0x382C81D8", "0x393B4D2C", "0x39140B50", "0x37E7F200", "0xB974C1D7", "0x36774D78", "0x36FC9613", "0x369251C0", "0xB500C1FF", "0x3414F62F", "0x2D5457C7", "0xB414F762", "0x3BE7BD60", "0x36440ABA", "0xB95F83C9", "0xBA3CBB65", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x36831A73", "0x32EF3947", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36470DED", "0x35FB37F3", "0x35B184AE", "0x35C15032", "0x382C5EDD", "0x393B07AA", "0x3913CE52", "0x00000000", "0x36774D78", "0x36FC9613", "0x369251C0", "0x3674C4D5", "0x36FAA8D0", "0x3691AC38"]}
{"command": "C0", "timestamp": 1755773883.4046109, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773883.6983097, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773884.093463, "raw_hex": "EB900101C4C00000008EAD3538832737398B1F0D39F1B6DEB8C24BEC3833F67C36857E0A371DB7A436045080B441C9E033337B62AECCC0E0B360BDE73BB5F2E934027172B9F452363A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000DC9E50B6CD9A3636000000000000000000000000000000008DFE2B400000803FF2555C367EC511366A22AF35AECFEA35551A353857BE363948C70C390000000033F67C36857E0A371DB7A436DDD37C36A8280A3796BDA336F2AAAB", "success": true, "data": null, "data_hex": "8EAD3538832737398B1F0D39F1B6DEB8C24BEC3833F67C36857E0A371DB7A436045080B441C9E033337B62AECCC0E0B360BDE73BB5F2E934027172B9F452363A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000DC9E50B6CD9A3636000000000000000000000000000000008DFE2B400000803FF2555C367EC511366A22AF35AECFEA35551A353857BE363948C70C390000000033F67C36857E0A371DB7A436DDD37C36A8280A3796BDA336", "data_hex_formatted": ["0x3835AD8E", "0x39372783", "0x390D1F8B", "0xB8DEB6F1", "0x38EC4BC2", "0x367CF633", "0x370A7E85", "0x36A4B71D", "0xB4805004", "0x33E0C941", "0xAE627B33", "0xB3E0C0CC", "0x3BE7BD60", "0x34E9F2B5", "0xB9727102", "0x3A3652F4", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB6509EDC", "0x36369ACD", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365C55F2", "0x3611C57E", "0x35AF226A", "0x35EACFAE", "0x38351A55", "0x3936BE57", "0x390CC748", "0x00000000", "0x367CF633", "0x370A7E85", "0x36A4B71D", "0x367CD3DD", "0x370A28A8", "0x36A3BD96"]}
{"command": "D0", "timestamp": 1755773884.3487225, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773884.6038113, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773884.873219, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773885.2638397, "raw_hex": "EB900101C4C00000006A34193862083A39E6821939E9864A39DCB54138F3D16D362BDC07371F98A8361A4F8AB4E5C0DEB30591A72E5692DE3360BDE73B6AC0F43543503A3AF2BD3BB90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CCF557B63D654F35000000000000000000000000000000008DFE2B400000803F99245D36C3200936BE54A1353EB8D93514B418388BD53939602C193900000000F3D16D362BDC07371F98A836D6BE6D36A2AE0737EE0EA83684AAAB", "success": true, "data": null, "data_hex": "6A34193862083A39E6821939E9864A39DCB54138F3D16D362BDC07371F98A8361A4F8AB4E5C0DEB30591A72E5692DE3360BDE73B6AC0F43543503A3AF2BD3BB90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CCF557B63D654F35000000000000000000000000000000008DFE2B400000803F99245D36C3200936BE54A1353EB8D93514B418388BD53939602C193900000000F3D16D362BDC07371F98A836D6BE6D36A2AE0737EE0EA836", "data_hex_formatted": ["0x3819346A", "0x393A0862", "0x391982E6", "0x394A86E9", "0x3841B5DC", "0x366DD1F3", "0x3707DC2B", "0x36A8981F", "0xB48A4F1A", "0xB3DEC0E5", "0x2EA79105", "0x33DE9256", "0x3BE7BD60", "0x35F4C06A", "0x3A3A5043", "0xB93BBDF2", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB657F5CC", "0x354F653D", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365D2499", "0x360920C3", "0x35A154BE", "0x35D9B83E", "0x3818B414", "0x3939D58B", "0x39192C60", "0x00000000", "0x366DD1F3", "0x3707DC2B", "0x36A8981F", "0x366DBED6", "0x3707AEA2", "0x36A80EEE"]}
{"command": "D3", "timestamp": 1755773885.519011, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773885.7872899, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773886.0519135, "raw_hex": "EB900101D5240000000000000000000000000000000000000000000000000000000000000000000000020002007AAAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000002000200", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0002", "0x0002"]}
{"command": "C4", "timestamp": 1755773886.4443364, "raw_hex": "EB900101C4C0000000785E2D38A6EB3C3963541739DE8B22394C965438A72C7436D4BE043722639F36D2A369B2947A80B473D4892D7B7C803460BDE73BFC06D5368E2F303AF13C38B90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000C108FCB5A12B0835000000000000000000000000000000008DFE2B400000803FDAFE4B36E58608360CC7BB35EB25D7356E2C2D3888E13C390050173900000000A72C7436D4BE043722639F36F9287436F3860437C9B09E36C4AAAB", "success": true, "data": null, "data_hex": "785E2D38A6EB3C3963541739DE8B22394C965438A72C7436D4BE043722639F36D2A369B2947A80B473D4892D7B7C803460BDE73BFC06D5368E2F303AF13C38B90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000C108FCB5A12B0835000000000000000000000000000000008DFE2B400000803FDAFE4B36E58608360CC7BB35EB25D7356E2C2D3888E13C390050173900000000A72C7436D4BE043722639F36F9287436F3860437C9B09E36", "data_hex_formatted": ["0x382D5E78", "0x393CEBA6", "0x39175463", "0x39228BDE", "0x3854964C", "0x36742CA7", "0x3704BED4", "0x369F6322", "0xB269A3D2", "0xB4807A94", "0x2D89D473", "0x34807C7B", "0x3BE7BD60", "0x36D506FC", "0x3A302F8E", "0xB9383CF1", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB5FC08C1", "0x35082BA1", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x364BFEDA", "0x360886E5", "0x35BBC70C", "0x35D725EB", "0x382D2C6E", "0x393CE188", "0x39175000", "0x00000000", "0x36742CA7", "0x3704BED4", "0x369F6322", "0x367428F9", "0x370486F3", "0x369EB0C9"]}
{"command": "C0", "timestamp": 1755773886.6832044, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773886.9771001, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773887.3564057, "raw_hex": "EB900101C4C0000000972F26383A413B39FD3C1639F6ADD9B7C03C3739F66F8036A81507371AFEA03604EF09B4F3EBFD337856AD2DA80AFEB360BDE73BB57E3136061F6D39C6C2363A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000004AD3603642BEFBB5000000000000000000000000000000008DFE2B400000803F3AAB5F36AFB6003652779E357916CE358C122638C7153B39EFEF153900000000F66F8036A81507371AFEA036535E803692ED06373CBBA036FFAAAB", "success": true, "data": null, "data_hex": "972F26383A413B39FD3C1639F6ADD9B7C03C3739F66F8036A81507371AFEA03604EF09B4F3EBFD337856AD2DA80AFEB360BDE73BB57E3136061F6D39C6C2363A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000004AD3603642BEFBB5000000000000000000000000000000008DFE2B400000803F3AAB5F36AFB6003652779E357916CE358C122638C7153B39EFEF153900000000F66F8036A81507371AFEA036535E803692ED06373CBBA036", "data_hex_formatted": ["0x38262F97", "0x393B413A", "0x39163CFD", "0xB7D9ADF6", "0x39373CC0", "0x36806FF6", "0x370715A8", "0x36A0FE1A", "0xB409EF04", "0x33FDEBF3", "0x2DAD5678", "0xB3FE0AA8", "0x3BE7BD60", "0x36317EB5", "0x396D1F06", "0x3A36C2C6", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x3660D34A", "0xB5FBBE42", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365FAB3A", "0x3600B6AF", "0x359E7752", "0x35CE1679", "0x3826128C", "0x393B15C7", "0x3915EFEF", "0x00000000", "0x36806FF6", "0x370715A8", "0x36A0FE1A", "0x36805E53", "0x3706ED92", "0x36A0BB3C"]}
{"command": "D0", "timestamp": 1755773887.6118748, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773887.8669114, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773888.1358976, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773888.528994, "raw_hex": "EB900101C4C0000000D12E1A38318833393F371339DFD9A0B8CFB21839EC857336C26C023702359C36FEE8A634AEAEBF32A32568ADACABBFB260BDE73BD1ECF035FF155F39DACC223A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000556980B5B71D8936000000000000000000000000000000008DFE2B400000803F2F6E7636DB28123640E2AE358506F335F68F18388C4B33399225133900000000EC857336C26C023702359C36A159713661E90137F7069C36D8AAAB", "success": true, "data": null, "data_hex": "D12E1A38318833393F371339DFD9A0B8CFB21839EC857336C26C023702359C36FEE8A634AEAEBF32A32568ADACABBFB260BDE73BD1ECF035FF155F39DACC223A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000556980B5B71D8936000000000000000000000000000000008DFE2B400000803F2F6E7636DB28123640E2AE358506F335F68F18388C4B33399225133900000000EC857336C26C023702359C36A159713661E90137F7069C36", "data_hex_formatted": ["0x381A2ED1", "0x39338831", "0x3913373F", "0xB8A0D9DF", "0x3918B2CF", "0x367385EC", "0x37026CC2", "0x369C3502", "0x34A6E8FE", "0x32BFAEAE", "0xAD6825A3", "0xB2BFABAC", "0x3BE7BD60", "0x35F0ECD1", "0x395F15FF", "0x3A22CCDA", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB5806955", "0x36891DB7", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36766E2F", "0x361228DB", "0x35AEE240", "0x35F30685", "0x38188FF6", "0x39334B8C", "0x39132592", "0x00000000", "0x367385EC", "0x37026CC2", "0x369C3502", "0x367159A1", "0x3701E961", "0x369C06F7"]}
{"command": "D3", "timestamp": 1755773888.7837906, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773889.0519855, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773889.3197687, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000500050080AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000005000500", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0005", "0x0005"]}
{"command": "C4", "timestamp": 1755773889.7117803, "raw_hex": "EB900101C4C000000000742638706D3E39096B1839BC2946B9E7D3CF388DEC843649870E37D908A5363B2898339C941BB4B5C8BA2D2A8C1B3460BDE73BF8D4563676CEFBB9D651103A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CC9C59B5551A6936000000000000000000000000000000008DFE2B400000803F993D5B36E34603361EB4B535CB20DB3530502638163E3E3970441839000000008DEC843649870E37D908A536FBD58436597B0E37E2F7A43680AAAB", "success": true, "data": null, "data_hex": "00742638706D3E39096B1839BC2946B9E7D3CF388DEC843649870E37D908A5363B2898339C941BB4B5C8BA2D2A8C1B3460BDE73BF8D4563676CEFBB9D651103A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000CC9C59B5551A6936000000000000000000000000000000008DFE2B400000803F993D5B36E34603361EB4B535CB20DB3530502638163E3E3970441839000000008DEC843649870E37D908A536FBD58436597B0E37E2F7A436", "data_hex_formatted": ["0x38267400", "0x393E6D70", "0x39186B09", "0xB94629BC", "0x38CFD3E7", "0x3684EC8D", "0x370E8749", "0x36A508D9", "0x3398283B", "0xB41B949C", "0x2DBAC8B5", "0x341B8C2A", "0x3BE7BD60", "0x3656D4F8", "0xB9FBCE76", "0x3A1051D6", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB5599CCC", "0x36691A55", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365B3D99", "0x360346E3", "0x35B5B41E", "0x35DB20CB", "0x38265030", "0x393E3E16", "0x39184470", "0x00000000", "0x3684EC8D", "0x370E8749", "0x36A508D9", "0x3684D5FB", "0x370E7B59", "0x36A4F7E2"]}
{"command": "C0", "timestamp": 1755773889.9547925, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773890.2484329, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773890.641276, "raw_hex": "EB900101C4C00000005A0D2638BAC635398E491139DADDE338D78D5AB9E9B27C3659220C3763E9AC3672E333B470D775346CBE622F94BF75B460BDE73BF31ED8355CC49338D9BC48BA0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000053B3D436DF3D09B5000000000000000000000000000000008DFE2B400000803F41B55B3651201236972597354D28D635F6712438F6CD343911F70F3900000000E9B27C3659220C3763E9AC36AB357C3665D70B37919AAC36ADAAAB", "success": true, "data": null, "data_hex": "5A0D2638BAC635398E491139DADDE338D78D5AB9E9B27C3659220C3763E9AC3672E333B470D775346CBE622F94BF75B460BDE73BF31ED8355CC49338D9BC48BA0000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000053B3D436DF3D09B5000000000000000000000000000000008DFE2B400000803F41B55B3651201236972597354D28D635F6712438F6CD343911F70F3900000000E9B27C3659220C3763E9AC36AB357C3665D70B37919AAC36", "data_hex_formatted": ["0x38260D5A", "0x3935C6BA", "0x3911498E", "0x38E3DDDA", "0xB95A8DD7", "0x367CB2E9", "0x370C2259", "0x36ACE963", "0xB433E372", "0x3475D770", "0x2F62BE6C", "0xB475BF94", "0x3BE7BD60", "0x35D81EF3", "0x3893C45C", "0xBA48BCD9", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x36D4B353", "0xB5093DDF", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365BB541", "0x36122051", "0x35972597", "0x35D6284D", "0x382471F6", "0x3934CDF6", "0x390FF711", "0x00000000", "0x367CB2E9", "0x370C2259", "0x36ACE963", "0x367C35AB", "0x370BD765", "0x36AC9A91"]}
{"command": "D0", "timestamp": 1755773890.8956156, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773891.1498117, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773891.417946, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773891.8104277, "raw_hex": "EB900101C4C00000000C8A153858973139AA2A0F39A2D302B96746DC38FA1A7B36B7C50A378569A83699C21E341C5B31B3C6D8F62D2A8A313360BDE73BDD9CD035EC8B54B9D8D0333A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000008E0898368FE70A36000000000000000000000000000000008DFE2B400000803FE15956365D34EF352DC494358DE3AE35ECFB1438422F313920E50E3900000000FA1A7B36B7C50A378569A83664BD7A36EBA10A3764B1A736F2AAAB", "success": true, "data": null, "data_hex": "0C8A153858973139AA2A0F39A2D302B96746DC38FA1A7B36B7C50A378569A83699C21E341C5B31B3C6D8F62D2A8A313360BDE73BDD9CD035EC8B54B9D8D0333A0000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000008E0898368FE70A36000000000000000000000000000000008DFE2B400000803FE15956365D34EF352DC494358DE3AE35ECFB1438422F313920E50E3900000000FA1A7B36B7C50A378569A83664BD7A36EBA10A3764B1A736", "data_hex_formatted": ["0x38158A0C", "0x39319758", "0x390F2AAA", "0xB902D3A2", "0x38DC4667", "0x367B1AFA", "0x370AC5B7", "0x36A86985", "0x341EC299", "0xB3315B1C", "0x2DF6D8C6", "0x33318A2A", "0x3BE7BD60", "0x35D09CDD", "0xB9548BEC", "0x3A33D0D8", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x3698088E", "0x360AE78F", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365659E1", "0x35EF345D", "0x3594C42D", "0x35AEE38D", "0x3814FBEC", "0x39312F42", "0x390EE520", "0x00000000", "0x367B1AFA", "0x370AC5B7", "0x36A86985", "0x367ABD64", "0x370AA1EB", "0x36A7B164"]}
{"command": "D3", "timestamp": 1755773892.0657682, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773892.3337076, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773892.6035428, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000000000076AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773892.995258, "raw_hex": "EB900101C4C00000006EC01B3802AC373902341539384F64B99A7597B8713B7E3647460937E115A23659F0EBB31F2510B473815E2DDD2F103460BDE73B50420736266C3BBA606AAF370000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000B746C435E628F234000000000000000000000000000000008DFE2B400000803F547D42365035E035362C87351A749D35DFFC1A38AE9E37390533153900000000713B7E3647460937E115A236FA7A7C3616F0083747CFA136BAAAAB", "success": true, "data": null, "data_hex": "6EC01B3802AC373902341539384F64B99A7597B8713B7E3647460937E115A23659F0EBB31F2510B473815E2DDD2F103460BDE73B50420736266C3BBA606AAF370000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000B746C435E628F234000000000000000000000000000000008DFE2B400000803F547D42365035E035362C87351A749D35DFFC1A38AE9E37390533153900000000713B7E3647460937E115A236FA7A7C3616F0083747CFA136", "data_hex_formatted": ["0x381BC06E", "0x3937AC02", "0x39153402", "0xB9644F38", "0xB897759A", "0x367E3B71", "0x37094647", "0x36A215E1", "0xB3EBF059", "0xB410251F", "0x2D5E8173", "0x34102FDD", "0x3BE7BD60", "0x36074250", "0xBA3B6C26", "0x37AF6A60", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x35C446B7", "0x34F228E6", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36427D54", "0x35E03550", "0x35872C36", "0x359D741A", "0x381AFCDF", "0x39379EAE", "0x39153305", "0x00000000", "0x367E3B71", "0x37094647", "0x36A215E1", "0x367C7AFA", "0x3708F016", "0x36A1CF47"]}
{"command": "C0", "timestamp": 1755773893.2396724, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773893.5341346, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773893.9240987, "raw_hex": "EB900101C4C000000001930D38C35635397B4D17394B6224395620CF38BC7D803648440437581E9836DC52E2325D3CC9348F5896ADCE3EC9B460BDE73B39202E3645BC303A40428EB60000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000A10FE8B527F290B5000000000000000000000000000000008DFE2B400000803F4F81603607CD1336566F9935839ED835CE7D0D38C04B35398B46173900000000BC7D803648440437581E9836CF51803661E003371359973698AAAB", "success": true, "data": null, "data_hex": "01930D38C35635397B4D17394B6224395620CF38BC7D803648440437581E9836DC52E2325D3CC9348F5896ADCE3EC9B460BDE73B39202E3645BC303A40428EB60000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000A10FE8B527F290B5000000000000000000000000000000008DFE2B400000803F4F81603607CD1336566F9935839ED835CE7D0D38C04B35398B46173900000000BC7D803648440437581E9836CF51803661E0033713599736", "data_hex_formatted": ["0x380D9301", "0x393556C3", "0x39174D7B", "0x3924624B", "0x38CF2056", "0x36807DBC", "0x37044448", "0x36981E58", "0x32E252DC", "0x34C93C5D", "0xAD96588F", "0xB4C93ECE", "0x3BE7BD60", "0x362E2039", "0x3A30BC45", "0xB68E4240", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB5E80FA1", "0xB590F227", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3660814F", "0x3613CD07", "0x35996F56", "0x35D89E83", "0x380D7DCE", "0x39354BC0", "0x3917468B", "0x00000000", "0x36807DBC", "0x37044448", "0x36981E58", "0x368051CF", "0x3703E061", "0x36975913"]}
{"command": "D0", "timestamp": 1755773894.1777275, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773894.4326854, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773894.70144, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773895.0919483, "raw_hex": "EB900101C4C00000001881143844CE3839B2331839B97E2AB9DD1355375E04783620220537320C9F36C924D2B37860C4B3291897AC5F68C43360BDE73B18632636139A1FBADC62E6390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000000242BC363BCE92B6000000000000000000000000000000008DFE2B400000803F9E9D5D360881F435A6A9983554F7C4356DC213388B683839D7BA1739000000005E04783620220537320C9F360DFF7636C2E30437A5C79E36BAAAAB", "success": true, "data": null, "data_hex": "1881143844CE3839B2331839B97E2AB9DD1355375E04783620220537320C9F36C924D2B37860C4B3291897AC5F68C43360BDE73B18632636139A1FBADC62E6390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000000242BC363BCE92B6000000000000000000000000000000008DFE2B400000803F9E9D5D360881F435A6A9983554F7C4356DC213388B683839D7BA1739000000005E04783620220537320C9F360DFF7636C2E30437A5C79E36", "data_hex_formatted": ["0x38148118", "0x3938CE44", "0x391833B2", "0xB92A7EB9", "0x375513DD", "0x3678045E", "0x37052220", "0x369F0C32", "0xB3D224C9", "0xB3C46078", "0xAC971829", "0x33C4685F", "0x3BE7BD60", "0x36266318", "0xBA1F9A13", "0x39E662DC", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x36BC4202", "0xB692CE3B", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365D9D9E", "0x35F48108", "0x3598A9A6", "0x35C4F754", "0x3813C26D", "0x3938688B", "0x3917BAD7", "0x00000000", "0x3678045E", "0x37052220", "0x369F0C32", "0x3676FF0D", "0x3704E3C2", "0x369EC7A5"]}
{"command": "D3", "timestamp": 1755773895.3471239, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773895.614591, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773895.8852012, "raw_hex": "EB900101D5240000000000000000000000000000000000000000000000000000000000000000000000030003007CAAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000003000300", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0003", "0x0003"]}
{"command": "C4", "timestamp": 1755773896.2789567, "raw_hex": "EB900101C4C00000005F9F1F3822053A39CD01173921FC91B8E00D34B9F3AA8B36373C1137CEDBA636A91CB632B28E8E3351D6F2AA878D8EB360BDE73B954E43363E5C1DBA257CDCB90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000931B8936760AAFB6000000000000000000000000000000008DFE2B400000803F419E5B36E2E8F0358F30A235C7F4C935EFC21A38C7BF393942DD163900000000F3AA8B36373C1137CEDBA6364AA88B36953511378ACEA6363DAAAB", "success": true, "data": null, "data_hex": "5F9F1F3822053A39CD01173921FC91B8E00D34B9F3AA8B36373C1137CEDBA636A91CB632B28E8E3351D6F2AA878D8EB360BDE73B954E43363E5C1DBA257CDCB90000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000931B8936760AAFB6000000000000000000000000000000008DFE2B400000803F419E5B36E2E8F0358F30A235C7F4C935EFC21A38C7BF393942DD163900000000F3AA8B36373C1137CEDBA6364AA88B36953511378ACEA636", "data_hex_formatted": ["0x381F9F5F", "0x393A0522", "0x391701CD", "0xB891FC21", "0xB9340DE0", "0x368BAAF3", "0x37113C37", "0x36A6DBCE", "0x32B61CA9", "0x338E8EB2", "0xAAF2D651", "0xB38E8D87", "0x3BE7BD60", "0x36434E95", "0xBA1D5C3E", "0xB9DC7C25", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x36891B93", "0xB6AF0A76", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365B9E41", "0x35F0E8E2", "0x35A2308F", "0x35C9F4C7", "0x381AC2EF", "0x3939BFC7", "0x3916DD42", "0x00000000", "0x368BAAF3", "0x37113C37", "0x36A6DBCE", "0x368BA84A", "0x37113595", "0x36A6CE8A"]}
{"command": "C0", "timestamp": 1755773896.5224056, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773896.8170483, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773897.209144, "raw_hex": "EB900101C4C0000000CA7A343849D63F399F621639B7CAD0B86E2D34399ECD7F3670130737AAC69D36B79A1535146C2DB464FF9CADEE6A2D3460BDE73B4FC09D35A06841B87675443A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000E9E15636F564DD33000000000000000000000000000000008DFE2B400000803F42C254362948F835AAF79C35A938BE351196333824D03F39D84C1639000000009ECD7F3670130737AAC69D36BF7F7E36CC46063748659C3607AAAB", "success": true, "data": null, "data_hex": "CA7A343849D63F399F621639B7CAD0B86E2D34399ECD7F3670130737AAC69D36B79A1535146C2DB464FF9CADEE6A2D3460BDE73B4FC09D35A06841B87675443A0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000E9E15636F564DD33000000000000000000000000000000008DFE2B400000803F42C254362948F835AAF79C35A938BE351196333824D03F39D84C1639000000009ECD7F3670130737AAC69D36BF7F7E36CC46063748659C36", "data_hex_formatted": ["0x38347ACA", "0x393FD649", "0x3916629F", "0xB8D0CAB7", "0x39342D6E", "0x367FCD9E", "0x37071370", "0x369DC6AA", "0x35159AB7", "0xB42D6C14", "0xAD9CFF64", "0x342D6AEE", "0x3BE7BD60", "0x359DC04F", "0xB84168A0", "0x3A447576", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x3656E1E9", "0x33DD64F5", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3654C242", "0x35F84829", "0x359CF7AA", "0x35BE38A9", "0x38339611", "0x393FD024", "0x39164CD8", "0x00000000", "0x367FCD9E", "0x37071370", "0x369DC6AA", "0x367E7FBF", "0x370646CC", "0x369C6548"]}
{"command": "D0", "timestamp": 1755773897.4638078, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773897.7188716, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773897.9871495, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773898.3779798, "raw_hex": "EB900101C4C00000007F9C273889342D39035C0839B41BFE37C14E10B94C1A7A36D5E0033756289B36392B88B341B66AB42FC1E62D6DB76A3460BDE73B97EEF93560C17BB9BCF725BA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000DEE37AB6F23B3736000000000000000000000000000000008DFE2B400000803F250956361709FD35B5F19C35F747C035DF63263879222D3939220839000000004C1A7A36D5E0033756289B364E9B79366ABC0337E90F9B369CAAAB", "success": true, "data": null, "data_hex": "7F9C273889342D39035C0839B41BFE37C14E10B94C1A7A36D5E0033756289B36392B88B341B66AB42FC1E62D6DB76A3460BDE73B97EEF93560C17BB9BCF725BA0000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000DEE37AB6F23B3736000000000000000000000000000000008DFE2B400000803F250956361709FD35B5F19C35F747C035DF63263879222D3939220839000000004C1A7A36D5E0033756289B364E9B79366ABC0337E90F9B36", "data_hex_formatted": ["0x38279C7F", "0x392D3489", "0x39085C03", "0x37FE1BB4", "0xB9104EC1", "0x367A1A4C", "0x3703E0D5", "0x369B2856", "0xB3882B39", "0xB46AB641", "0x2DE6C12F", "0x346AB76D", "0x3BE7BD60", "0x35F9EE97", "0xB97BC160", "0xBA25F7BC", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB67AE3DE", "0x36373BF2", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36560925", "0x35FD0917", "0x359CF1B5", "0x35C047F7", "0x382663DF", "0x392D2279", "0x39082239", "0x00000000", "0x367A1A4C", "0x3703E0D5", "0x369B2856", "0x36799B4E", "0x3703BC6A", "0x369B0FE9"]}
{"command": "D3", "timestamp": 1755773898.6316807, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773898.899334, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773899.1549132, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000600060082AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000006000600", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0006", "0x0006"]}
{"command": "C4", "timestamp": 1755773899.5457006, "raw_hex": "EB900101C4C000000082551D38FCEE32390CF610399BCD8738DBD4EA3890A37A362E3B08377209A536BB8E0635171293B433950E2E8C10933460BDE73B57E48F361E21283AFDA663390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000006762E2358D124936000000000000000000000000000000008DFE2B400000803FADD65C362880EE3584F29B35F457B035FA291D3800B132399AC410390000000090A37A362E3B08377209A536B00D783689960737DD4CA436A4AAAB", "success": true, "data": null, "data_hex": "82551D38FCEE32390CF610399BCD8738DBD4EA3890A37A362E3B08377209A536BB8E0635171293B433950E2E8C10933460BDE73B57E48F361E21283AFDA663390000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000006762E2358D124936000000000000000000000000000000008DFE2B400000803FADD65C362880EE3584F29B35F457B035FA291D3800B132399AC410390000000090A37A362E3B08377209A536B00D783689960737DD4CA436", "data_hex_formatted": ["0x381D5582", "0x3932EEFC", "0x3910F60C", "0x3887CD9B", "0x38EAD4DB", "0x367AA390", "0x37083B2E", "0x36A50972", "0x35068EBB", "0xB4931217", "0x2E0E9533", "0x3493108C", "0x3BE7BD60", "0x368FE457", "0x3A28211E", "0x3963A6FD", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x35E26267", "0x3649128D", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x365CD6AD", "0x35EE8028", "0x359BF284", "0x35B057F4", "0x381D29FA", "0x3932B100", "0x3910C49A", "0x00000000", "0x367AA390", "0x37083B2E", "0x36A50972", "0x36780DB0", "0x37079689", "0x36A44CDD"]}
{"command": "C0", "timestamp": 1755773899.7883525, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773900.0828693, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773900.475817, "raw_hex": "EB900101C4C00000001283263872DD2A39148A0539BA0B2EB99C0804B9AD0A733664C40937F534A736075CA8B4E03000B415BEB5AE963D003460BDE73B4442DF35649637BA9AF48DB90000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000001C8A8B365C4ABDB5000000000000000000000000000000008DFE2B400000803F10E8663609D6043667E59E35C77ED63563D1213844122A39B342053900000000AD0A733664C40937F534A73699747236C581093711D7A636DFAAAB", "success": true, "data": null, "data_hex": "1283263872DD2A39148A0539BA0B2EB99C0804B9AD0A733664C40937F534A736075CA8B4E03000B415BEB5AE963D003460BDE73B4442DF35649637BA9AF48DB90000000000000000000000000000000000000000000000007DAE2A3F0000000000000000000000001C8A8B365C4ABDB5000000000000000000000000000000008DFE2B400000803F10E8663609D6043667E59E35C77ED63563D1213844122A39B342053900000000AD0A733664C40937F534A73699747236C581093711D7A636", "data_hex_formatted": ["0x38268312", "0x392ADD72", "0x39058A14", "0xB92E0BBA", "0xB904089C", "0x36730AAD", "0x3709C464", "0x36A734F5", "0xB4A85C07", "0xB40030E0", "0xAEB5BE15", "0x34003D96", "0x3BE7BD60", "0x35DF4244", "0xBA379664", "0xB98DF49A", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0x368B8A1C", "0xB5BD4A5C", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3666E810", "0x3604D609", "0x359EE567", "0x35D67EC7", "0x3821D163", "0x392A1244", "0x390542B3", "0x00000000", "0x36730AAD", "0x3709C464", "0x36A734F5", "0x36727499", "0x370981C5", "0x36A6D711"]}
{"command": "D0", "timestamp": 1755773900.730773, "raw_hex": "EB900101D02400000000000000000000000000000000000000000000000000000000000000000000000000000071AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D1", "timestamp": 1755773900.984967, "raw_hex": "EB900101D12400000000000000000000000000000000000000000000000000000000000000000000000000000072AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D2", "timestamp": 1755773901.2529173, "raw_hex": "EB900101D22400000000000000000000000000000000000000000000000000000000000000000000000000000073AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "C4", "timestamp": 1755773901.6436074, "raw_hex": "EB900101C4C00000005F432538446A37392E141239F9504839F88495377C337C366BA60B376931A736C10BB2B4D4A7FE33BF10A6ADE0B3FEB360BDE73B49E29636C2AA383A062E54B90000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000071618AB64A540DB6000000000000000000000000000000008DFE2B400000803F7C3E61364395F235A23D9F352B8CCC35069B24381121373917E61139000000007C337C366BA60B376931A73631767B36BE700B37C7CDA63641AAAB", "success": true, "data": null, "data_hex": "5F432538446A37392E141239F9504839F88495377C337C366BA60B376931A736C10BB2B4D4A7FE33BF10A6ADE0B3FEB360BDE73B49E29636C2AA383A062E54B90000000000000000000000000000000000000000000000007DAE2A3F00000000000000000000000071618AB64A540DB6000000000000000000000000000000008DFE2B400000803F7C3E61364395F235A23D9F352B8CCC35069B24381121373917E61139000000007C337C366BA60B376931A73631767B36BE700B37C7CDA636", "data_hex_formatted": ["0x3825435F", "0x39376A44", "0x3912142E", "0x394850F9", "0x379584F8", "0x367C337C", "0x370BA66B", "0x36A73169", "0xB4B20BC1", "0x33FEA7D4", "0xADA610BF", "0xB3FEB3E0", "0x3BE7BD60", "0x3696E249", "0x3A38AAC2", "0xB9542E06", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB68A6171", "0xB60D544A", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x36613E7C", "0x35F29543", "0x359F3DA2", "0x35CC8C2B", "0x38249B06", "0x39372111", "0x3911E617", "0x00000000", "0x367C337C", "0x370BA66B", "0x36A73169", "0x367B7631", "0x370B70BE", "0x36A6CDC7"]}
{"command": "D3", "timestamp": 1755773901.8978286, "raw_hex": "EB900101D32400000000000000000000000000000000000000000000000000000000000000000000000000000074AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D4", "timestamp": 1755773902.1662467, "raw_hex": "EB900101D42400000000000000000000000000000000000000000000000000000000000000000000000000000075AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
{"command": "D5", "timestamp": 1755773902.4329998, "raw_hex": "EB900101D52400000000000000000000000000000000000000000000000000000000000000000000000100010078AAAB", "success": true, "data": null, "data_hex": "000000000000000000000000000000000000000000000000000000000000000001000100", "data_hex_formatted": ["0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0001", "0x0001"]}
{"command": "C4", "timestamp": 1755773902.824334, "raw_hex": "EB900101C4C0000000D1A22C38ACAF413981A61939265A2839CE949A38EA5E7236BF550437D9909D3630B4A933669209B4A746752E3397093460BDE73BB544AB35EFBF3A3A20445CB70000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000F5E8C3B6CF8FA6B5000000000000000000000000000000008DFE2B400000803F53A75336A38BF3351FD9943538F0B735594B2B38665B4139A371193900000000EA5E7236BF550437D9909D36555372362B3D04378E5F9D364FAAAB", "success": true, "data": null, "data_hex": "D1A22C38ACAF413981A61939265A2839CE949A38EA5E7236BF550437D9909D3630B4A933669209B4A746752E3397093460BDE73BB544AB35EFBF3A3A20445CB70000000000000000000000000000000000000000000000007DAE2A3F000000000000000000000000F5E8C3B6CF8FA6B5000000000000000000000000000000008DFE2B400000803F53A75336A38BF3351FD9943538F0B735594B2B38665B4139A371193900000000EA5E7236BF550437D9909D36555372362B3D04378E5F9D36", "data_hex_formatted": ["0x382CA2D1", "0x3941AFAC", "0x3919A681", "0x39285A26", "0x389A94CE", "0x36725EEA", "0x370455BF", "0x369D90D9", "0x33A9B430", "0xB4099266", "0x2E7546A7", "0x34099733", "0x3BE7BD60", "0x35AB44B5", "0x3A3ABFEF", "0xB75C4420", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x3F2AAE7D", "0x00000000", "0x00000000", "0x00000000", "0xB6C3E8F5", "0xB5A68FCF", "0x00000000", "0x00000000", "0x00000000", "0x00000000", "0x402BFE8D", "0x3F800000", "0x3653A753", "0x35F38BA3", "0x3594D91F", "0x35B7F038", "0x382B4B59", "0x39415B66", "0x391971A3", "0x00000000", "0x36725EEA", "0x370455BF", "0x369D90D9", "0x36725355", "0x37043D2B", "0x369D5F8E"]}
{"command": "C0", "timestamp": 1755773903.068137, "raw_hex": "EB900101C0080000000000000050A4000039AAAB", "success": true, "data": null, "data_hex": "0000000050A40000", "data_hex_formatted": ["0x00000000", "0x0000A450"]}
{"command": "C1", "timestamp": 1755773903.3619657, "raw_hex": "EB900101C1440000000000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000BCAAAB", "success": true, "data": null, "data_hex": "0000000044150880000000000000000000000000000008000800080001004000000000000000000000000000000000000000000000000000000000000000000000000000", "data_hex_formatted": ["0x0000", "0x0000", "0x1544", "0x8008", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0008", "0x0008", "0x0008", "0x0001", "0x0040", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000", "0x0000"]}
